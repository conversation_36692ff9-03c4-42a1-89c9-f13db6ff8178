import { filterUndefined, normalizeImportHeader } from '@pocitarna-nx-2023/utils';
import * as XLSX from 'xlsx';
import type { Column } from '../../types';
import { BaseFileExtension } from '../../utils/BaseFileExtension';

export class XLSXFileExtension extends BaseFileExtension {
	public parseFile<T extends Column>(buffer: Uint8Array, columns: T[]): Record<string, unknown>[] {
		const workbook = XLSX.read(buffer, { type: 'array' });

		const sheetNames = workbook.SheetNames;
		const jsonData = sheetNames.flatMap((sheetName) => {
			const sheet = workbook.Sheets[sheetName];
			const headerRow = (XLSX.utils.sheet_to_json(sheet, { header: 1 })[0] as string[]).map((column) =>
				normalizeImportHeader(column),
			); // Assuming the first row is the header and everything is string

			const expectedColumns = columns.map((c) => (Array.isArray(c.name) ? c.name[0] : c.name));
			if (headerRow.length !== expectedColumns.length) return [];

			const isHeaderRowValid = (headerRow: string[], expectedColumns: string[]): boolean => {
				return headerRow.every((column, index) => column === expectedColumns[index]);
			};

			const valid = isHeaderRowValid(headerRow, expectedColumns);

			if (valid) {
				const sheetData = XLSX.utils.sheet_to_json(sheet, {
					header: columns.map((c) => normalizeImportHeader(Array.isArray(c.name) ? c.name[0] : c.name)),
				}) as Record<string, unknown>[];
				return sheetData.slice(1);
			}
			return [];
		});

		return jsonData;
	}

	public compatibleWith(buffer: Uint8Array, columns: Column[][]) {
		const workbook = XLSX.read(buffer, { type: 'array' });

		const result = filterUndefined(
			workbook.SheetNames.map((sheetName) => {
				const sheet = workbook.Sheets[sheetName];
				const headerRow = (XLSX.utils.sheet_to_json(sheet, { header: 1 })[0] as string[]).map((column) =>
					normalizeImportHeader(column),
				); // Assuming the first row is the header and everything is string

				const valid = columns.find((col) => {
					const expectedColumns = col.map((c) => (Array.isArray(c.name) ? c.name[0] : c.name));
					if (headerRow.length !== expectedColumns.length) {
						return;
					}

					const isHeaderRowValid = (headerRow: string[], expectedColumns: string[]): boolean => {
						return headerRow.every((column, index) => column === expectedColumns[index]);
					};

					return isHeaderRowValid(headerRow, expectedColumns);
				});

				if (valid) {
					return {
						sheetName,
						columns: valid,
					};
				}
				return;
			}),
		);

		return result[0]?.columns;
	}
}
