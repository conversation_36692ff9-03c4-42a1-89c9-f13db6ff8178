import { ProductCategoryController, ProductCodeController, ProductController } from '@pocitarna-nx-2023/database';
import { safeDivide } from '@pocitarna-nx-2023/utils';
import { BaseVariantManager } from '../../utils/BaseVariantManager';

export class Quantity extends BaseVariantManager {
	constructor(batchId: string) {
		super(
			{
				extension: 'xlsx',
				name: 'Quantity',
				currency: 'EUR',
				productNumberColumn: '',
				quantityColumn: 'počet kusů',
				columns: [
					[
						{ name: 'kategorie', type: 'string', required: true },
						{ name: 'počet kus<PERSON>', type: 'number', required: true },
						{ name: 'cena bez dph', type: 'number' },
					],
				],
			},
			batchId,
		);
	}

	override async processRow(data: Record<string, unknown>) {
		const product = await new ProductController().create({
			status: 'AT_SUPPLIER',
			batch: { id: this.batchId },
			files: this.files,
		});

		const category = await new ProductCategoryController().findByCodePrefix(data['kategorie'] as string);
		if (category) {
			await new ProductController().changeCategory(product.id, category.id);
		}

		await new ProductController().addBuyPrice(product.id, safeDivide(data['cena bez dph'] as number, data['počet kusů'] as number));
		await new ProductCodeController().create({ batch: { id: this.batchId } });

		return product;
	}
}
