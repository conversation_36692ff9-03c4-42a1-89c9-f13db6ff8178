import { AttributeController, listOne, ProductController } from '@pocitarna-nx-2023/database';
import { getProductStatusesBelow } from '@pocitarna-nx-2023/utils';
import { BaseVariantManager } from '../../utils/BaseVariantManager';

export class SN extends BaseVariantManager {
	constructor(batchId: string) {
		super(
			{
				extension: 'xlsx',
				name: 'SN',
				currency: 'EUR',
				productNumberColumn: 'p/n',
				columns: [
					[
						{ name: 's/n', type: 'string', required: true },
						{ name: 'p/n', type: 'string', required: true },
					],
				],
			},
			batchId,
		);
	}

	override async processRow(data: Record<string, unknown>) {
		const snAlreadyExists = await new ProductController().findBySnInBatch(this.batchId, data['s/n'] as string);
		if (snAlreadyExists) return null;

		const [{ attributeValueId }] = await new AttributeController().getOrCreateValue('PN', data['p/n'] as string, false);
		if (!attributeValueId) return null;

		const [[product]] = await new ProductController().findByAttributeValues(
			[attributeValueId],
			listOne({ filter: { sn: { eq: '' }, status: { eq: getProductStatusesBelow('TO_TEST') }, batchId: { eq: this.batchId } } }),
		);
		if (!product) throw new Error(`Ve várce není produkt s PN "${data['p/n']}" bez SN`);

		await new ProductController().update(product, { sn: data['s/n'] as string });

		return product;
	}
}
