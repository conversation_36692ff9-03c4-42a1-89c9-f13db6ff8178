import { AttributeController, listAll, ProductController } from '@pocitarna-nx-2023/database';
import { getProductStatusesBelow } from '@pocitarna-nx-2023/utils';
import { BaseVariantManager } from '../../utils/BaseVariantManager';

export class Prices extends BaseVariantManager {
	constructor(batchId: string) {
		super(
			{
				extension: 'xlsx',
				name: 'Prices',
				currency: 'EUR',
				productNumberColumn: '',
				columns: [
					[
						{ name: 'pn/sn/model', type: 'string', required: true },
						{ name: 'cena bez dph', type: 'number', required: true },
					],
				],
			},
			batchId,
		);
	}

	override async processRow(data: Record<string, unknown>) {
		const value = data['pn/sn/model'] as string;
		const productBySn = await new ProductController().findBySnInBatch(this.batchId, value);
		if (productBySn) {
			await new ProductController().addBuyPrice(productBySn.id, data['cena bez dph'] as number);
			await this.handleSalePrice(data, productBySn.id);
			await this.handleStandardPrice(data, productBySn.id);
			return productBySn;
		}

		const attributeValue = await new AttributeController().findValueInAllAttributes(value);
		if (!attributeValue) return null;
		const { attributeValueId } = attributeValue;

		const [products] = await new ProductController().findByAttributeValues(
			[attributeValueId],
			listAll({ filter: { status: { eq: getProductStatusesBelow('STOCK') }, batchId: { eq: this.batchId } } }),
		);
		if (!products) return null;

		await Promise.all(
			products.map(async (product) => {
				await new ProductController().addBuyPrice(product.id, data['cena bez dph'] as number);
				await this.handleSalePrice(data, product.id);
				await this.handleStandardPrice(data, product.id);
			}),
		);

		return null;
	}
}
