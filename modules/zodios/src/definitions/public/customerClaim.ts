import { makeApi } from '@zodios/core';
import { z } from 'zod';
import { customerClaim, errors, listProps } from '../../entities';
import { apiResponse } from '../../utils/wrapper';

export const publicCustomerClaimApi = makeApi([
	{
		method: 'get',
		path: '/',
		alias: 'getPublicCustomerClaims',
		response: apiResponse(z.array(customerClaim)),
		parameters: listProps,
		errors,
	},
	{
		method: 'get',
		path: '/:customerClaimId',
		alias: 'getPublicCustomerClaim',
		response: apiResponse(customerClaim),
		parameters: [{ name: 'customerClaimId', type: 'Path', schema: z.string().uuid() }],
		errors,
	},
]);
