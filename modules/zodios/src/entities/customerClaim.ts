import { CUSTOMER_CLAIM_STATUS_NAMES } from '@pocitarna-nx-2023/config';
import { z } from 'zod';
import { code } from './code';
import { note } from './note';
import { user } from './user';

export const customerClaim = z.object({
	id: z.string().uuid(),
	createdAt: z.coerce.date(),
	createdBy: user.nullish().default(null),
	createdById: z.string().uuid().nullish().default(null),
	status: z.enum(CUSTOMER_CLAIM_STATUS_NAMES),
	code,
	codeId: z.string().uuid(),
	notes: z.array(note).default([]),
});

export const customerClaimCreate = z.object({
	note: z.string(),
	status: z.enum(CUSTOMER_CLAIM_STATUS_NAMES).optional(),
	productDefects: z.array(z.object({ id: z.string().uuid() })),
	productId: z.string().uuid(),
});

export const customerClaimUpdate = z.object({
	status: z.enum(CUSTOMER_CLAIM_STATUS_NAMES),
	productId: z.string().uuid(),
});
