import { PRICE_ROUNDING_STRATEGIES } from '@pocitarna-nx-2023/config';
import { z } from 'zod';

export const grade = z.object({
	id: z.string().uuid(),
	name: z.string(),
	maxCosmeticDefects: z.coerce.number(),
	discountPercentage: z.coerce.number(),
	priceRoundingStrategy: z.enum(PRICE_ROUNDING_STRATEGIES),
	sequence: z.number().min(0),
});

export const gradeCreate = z.object({
	name: z.string(),
	maxCosmeticDefects: z.coerce.number(),
	discountPercentage: z.coerce.number().transform((val) => {
		return val > 1 ? val / 100 : val;
	}),
	priceRoundingStrategy: z.enum(PRICE_ROUNDING_STRATEGIES),
	sequence: z.number().min(0),
});

export const gradeUpdate = gradeCreate.partial();
