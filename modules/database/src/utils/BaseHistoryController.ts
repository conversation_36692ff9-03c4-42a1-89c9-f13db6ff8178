import type { ListProps } from '@pocitarna-nx-2023/zodios';
import type { ObjectType, Repository, SelectQueryBuilder } from 'typeorm';
import type { QueryBuilderMiddleware } from '../types';
import { applyFilter } from './applyFilter';
import { applyPaging } from './applyPaging';
import { applySort } from './applySort';
import type { CommonEntity } from './CommonEntity';
import { getRepository } from './getRepository';
import { listAll } from './list';

export abstract class BaseHistoryController<Entity extends CommonEntity> {
	protected name: string;
	protected repository: Repository<Entity>;

	protected constructor(entity: ObjectType<Entity>) {
		this.name = entity.name;
		this.repository = getRepository(entity);
		const periodColumn = this.repository.metadata.ownColumns.find((column) => column.propertyName === 'period');
		if (periodColumn) {
			this.repository.metadata.hasMultiplePrimaryKeys = true;
			periodColumn.isPrimary = true;
			periodColumn.isSelect = true;
			this.repository.metadata.primaryColumns.push(periodColumn);
		}
	}

	protected getQueryBuilder(...middlewares: QueryBuilderMiddleware<Entity>[]) {
		return [this.includeActionMiddleware, ...middlewares].reduce(
			(queryBuilder, middleware) => middleware(queryBuilder),
			this.repository.createQueryBuilder('entity'),
		);
	}

	protected includeActionMiddleware: QueryBuilderMiddleware<Entity> = (qb) => {
		return qb
			.innerJoinAndSelect('entity.action', 'action')
			.innerJoinAndSelect('action.authentication', 'authentication')
			.innerJoinAndSelect('authentication.user', 'user');
	};

	protected applyProps({ filter, page }: ListProps) {
		return (queryBuilder: SelectQueryBuilder<Entity>) =>
			applyPaging(page)(applySort([['action.createdAt', 'desc']])(applyFilter(filter)(queryBuilder)));
	}

	protected async listQuery(props: ListProps, queryBuilder = this.getQueryBuilder()) {
		return this.applyProps(props)(queryBuilder.clone()).getMany();
	}

	listById(id: Entity['id']) {
		return this.listQuery(listAll({ filter: { id: { eq: id } } }));
	}
}
