import { filterUndefined, uniques } from '@pocitarna-nx-2023/utils';
import { type Repository } from 'typeorm';
import { CustomerClaim, type Product } from '../entity';
import { type QueryBuilderMiddleware } from '../types';
import { BaseController } from '../utils/BaseController';
import { listAll, listOne } from '../utils/list';
import { NoteController, ProductController, ProductDefectController } from '.';
import { CustomerClaimCodeController } from './CustomerClaimCodeController';

export class CustomerClaimController extends BaseController<CustomerClaim> {
	constructor() {
		super(CustomerClaim);
	}

	protected override getQueryBuilder(...middlewares: QueryBuilderMiddleware<CustomerClaim>[]) {
		return super
			.getQueryBuilder(...middlewares)
			.innerJoinAndSelect('entity.code', 'code')
			.innerJoinAndSelect('entity.createdBy', 'createdBy')
			.leftJoinAndSelect('entity.notes', 'note')
			.leftJoinAndSelect('note.createdBy', 'noteCreatedBy');
	}

	override async create(
		data: Omit<Parameters<Repository<CustomerClaim>['save']>[0], 'code'> & { note?: string; productId: string },
	): Promise<CustomerClaim> {
		const { productDefects, note, ...rest } = data;
		const code = await new CustomerClaimCodeController().create({});
		const customerClaim = await super.create({ ...rest, code });

		await new ProductController().update(data.productId, { status: 'CUSTOMER_CLAIM' });

		if (!customerClaim) throw new Error('Could not create customer claim');

		if (note) {
			await new NoteController().create({ content: note, customerClaim: { id: customerClaim.id } });
		}

		await Promise.all(
			(productDefects ?? []).map(async (productDefect) => {
				if (productDefect.id) {
					await new ProductDefectController().update(productDefect.id, { customerClaim: { id: customerClaim.id } });
				}
			}),
		);

		return customerClaim;
	}

	override async update(
		idOrRecord: CustomerClaim['id'] | CustomerClaim,
		data: Parameters<Repository<CustomerClaim>['update']>[1] & { productId: string },
	) {
		const currentClaim = await this.resolveRecord(idOrRecord);
		const currentStatus = currentClaim.status;
		const [productDefectsWithCustomerClaims] = await new ProductDefectController().list(
			listAll({
				filter: {
					productId: { eq: data.productId },
					customerClaimId: { ne: null },
				},
			}),
		);

		const otherCustomerClaimIds = filterUndefined(
			uniques(productDefectsWithCustomerClaims.map((defect) => defect.customerClaimId).filter((id) => id !== currentClaim.id)),
		);

		const [otherOpenClaims] = await this.list(
			listAll({
				filter: {
					id: { eq: otherCustomerClaimIds },
					status: { ne: 'CLOSED' },
				},
			}),
		);

		if (currentStatus !== 'CLOSED' && data.status === 'CLOSED' && otherOpenClaims.length === 0) {
			await new ProductController().update(data.productId, { status: 'SOLD' });
		}

		return await super.update(idOrRecord, data);
	}

	async getCustomerClaimByProduct(productId: Product['id']) {
		const [[productDefect]] = await new ProductDefectController().list(
			listOne({ filter: { productId: { eq: productId }, customerClaimId: { ne: null } } }),
		);
		if (!productDefect) return null;

		if (!productDefect.customerClaimId) return null;

		return this.findById(productDefect.customerClaimId);
	}
}
