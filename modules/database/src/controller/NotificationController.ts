import { NOTIFICATION_TITLES, type NotificationType } from '@pocitarna-nx-2023/config';
import { publishNotification } from '@pocitarna-nx-2023/sse-server';
import { type SingleOrArray } from '@pocitarna-nx-2023/utils';
import { randomUUID } from 'crypto';
import type { Repository } from 'typeorm';
import { Notification, type NotificationType as NotificationTypeEntity, type User, type UserNotificationType } from '../entity';
import { BaseController } from '../utils/BaseController';
import { listAll } from '../utils/list';
import { NotificationTypeController } from './NotificationTypeController';
import { UserNotificationTypeController } from './UserNotificationTypeController';

export class NotificationController extends BaseController<Notification> {
	constructor() {
		super(Notification);
	}

	override getQueryBuilder() {
		return super.getQueryBuilder().innerJoinAndSelect('entity.user', 'user');
	}

	async notify({
		data,
		notificationType,
		userId,
	}: {
		data: SingleOrArray<
			Omit<Parameters<Repository<Notification>['create']>[0], 'userId' | 'notificationType' | 'notificationCode' | 'title'> & {
				notificationCode?: string;
			}
		>;
		notificationType: NotificationType;
		userId?: SingleOrArray<User['id']>;
	}) {
		const notificationTypeEntity = await new NotificationTypeController().findByName(notificationType);
		if (!notificationTypeEntity) {
			throw new Error(`Notification type ${notificationType} not found`);
		}

		const dataArray = Array.isArray(data) ? data : [data];
		const subscriptions = await this.getNotificationSubscriptions(notificationTypeEntity.id, userId);

		if (subscriptions.length === 0) return;

		const payloads: Parameters<Repository<Notification>['create']>[0][] = [];

		for (const item of dataArray) {
			const code = item.notificationCode ?? randomUUID();

			for (const userNotificationType of subscriptions) {
				payloads.push({
					...item,
					title: NOTIFICATION_TITLES[notificationTypeEntity.name],
					userId: userNotificationType.userId,
					notificationCode: code,
					notificationType: { id: notificationTypeEntity.id },
				});
			}
		}

		const notifications = await super.bulkCreate(payloads);
		publishNotification(...notifications);
	}

	async getNotificationSubscriptions(
		notificationTypeId: NotificationTypeEntity['id'],
		userId?: SingleOrArray<User['id']>,
	): Promise<UserNotificationType[]> {
		const [subscribedUsers] = await new UserNotificationTypeController().listByNotificationType(notificationTypeId, {
			filter: { deliveryMethod: { eq: 'push' as const } },
		});

		if (!userId) {
			return subscribedUsers;
		}

		const userIdArray = Array.isArray(userId) ? userId : [userId];

		return subscribedUsers.filter((subscription) => userIdArray.includes(subscription.userId));
	}

	override async update(idOrRecord: Notification | Notification['id'], data: Parameters<Repository<Notification>['update']>[1]) {
		const notification = await super.update(idOrRecord, data);
		if (!notification) return notification;

		if (data.viewedAt) {
			const [relatedNotifications] = await this.list(
				listAll({ filter: { notificationCode: { eq: notification.notificationCode }, viewedAt: { eq: null } } }),
			);
			await Promise.all(relatedNotifications.map((notification) => super.update(notification, { viewedAt: data.viewedAt })));
		}

		return notification;
	}

	async findForRefire() {
		return this.list({ filter: { viewedAt: { eq: null }, refiredAt: { eq: null }, viewUntil: { lte: new Date() } } });
	}
}
