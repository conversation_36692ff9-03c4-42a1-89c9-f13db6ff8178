import { type InventoryItemStatus } from '@pocitarna-nx-2023/config';
import { formatEnvelopeCode, organizeInventoryItems, type SingleOrArray } from '@pocitarna-nx-2023/utils';
import { formatWarehousePositionName } from '@pocitarna-nx-2023/utils';
import { Decimal } from 'decimal.js';
import { type Repository } from 'typeorm';
import { type File, Inventory, type InventoryItem, type Product, type ServiceCase, type WarrantyClaim } from '../entity';
import { BaseController } from '../utils/BaseController';
import { listAll } from '../utils/list';
import { generateInventoryReport } from '../utils/reports';
import { CustomerClaimController } from './CustomerClaimController';
import { EcommerceOrderController } from './EcommerceOrderController';
import { FileInventoryController } from './FileInventoryController';
import { InventoryCodeController } from './InventoryCodeController';
import { InventoryItemController } from './InventoryItemController';
import { ServiceCaseController } from './ServiceCaseController';
import { WarrantyClaimController } from './WarrantyClaimController';

export class InventoryController extends BaseController<Inventory> {
	constructor() {
		super(Inventory);
	}

	override getQueryBuilder() {
		return super.getQueryBuilder().innerJoinAndSelect('entity.code', 'code').innerJoinAndSelect('entity.createdBy', 'createdBy');
	}

	override async create(data: Omit<Parameters<Repository<Inventory>['save']>[0], 'code'>): Promise<Inventory> {
		const code = await new InventoryCodeController().create({});

		return super.create({
			...data,
			code,
		});
	}

	async getInventoryItemRelatedData({ product, inventoryStatus }: { product: Product; inventoryStatus?: InventoryItemStatus }) {
		const buyPrice = product.productPrice.find((price) => price.type === 'BUY')?.value ?? 0;
		const warrantyClaim =
			product.status === 'WARRANTY_CLAIM' ? await new WarrantyClaimController().getWarrantyClaimByProduct(product.id) : null;
		const serviceCase = product.status === 'SERVICE' ? await new ServiceCaseController().getServiceCaseByProduct(product.id) : null;
		const ecommerceOrder = ['RESERVED', 'SOLD'].includes(product.status)
			? await new EcommerceOrderController().findByProduct(product.id)
			: null;
		const customerClaim =
			product.status === 'CUSTOMER_CLAIM' ? await new CustomerClaimController().getCustomerClaimByProduct(product.id) : null;

		const productEnvelopeName = product.productEnvelope?.name ?? null;
		const productEnvelopeCode =
			product.productEnvelope?.code && product.productEnvelope?.productCategory?.codePrefix
				? formatEnvelopeCode(product.productEnvelope?.productCategory?.codePrefix)(product.productEnvelope.code)
				: null;

		const inventoryStatusToSet =
			inventoryStatus ?? (await this.getInventoryItemInventoryStatus({ product, warrantyClaim, serviceCase }));
		const preScanWarehousePosition = product.warehousePosition;

		return {
			product: { id: product.id },
			batch: product.batchId ? { id: product.batchId } : null,
			status: product.status,
			inventoryStatus: inventoryStatusToSet,
			sn: product.sn,
			buyPrice,
			productEnvelope: product.productEnvelope,
			productEnvelopeCode,
			productEnvelopeName,
			warrantyClaim,
			warrantyClaimCode: warrantyClaim?.code?.code ?? null,
			serviceCase,
			serviceCaseCode: serviceCase?.code?.code ?? null,
			ecommerceOrder,
			ecommerceOrderCode: ecommerceOrder?.code ?? null,
			customerClaim,
			customerClaimCode: customerClaim?.code?.code ?? null,
			productCategory: product.productCategory,
			preScanWarehousePosition,
			preScanWarehousePositionName: preScanWarehousePosition
				? `${preScanWarehousePosition.warehouse.name} - ${formatWarehousePositionName(preScanWarehousePosition)}`
				: null,
		};
	}

	async getInventoryItemInventoryStatus({
		product,
		warrantyClaim,
		serviceCase,
	}: {
		product: Product;
		warrantyClaim: WarrantyClaim | null;
		serviceCase: ServiceCase | null;
	}) {
		if (['ON_THE_WAY', 'AT_SUPPLIER', 'EXTERNAL_STOCK'].includes(product.status)) return 'OK';

		if (warrantyClaim) {
			if (['SENT_TO_VENDOR', 'WAITING_FOR_REPAIR', 'WAITING_FOR_RETURN'].includes(warrantyClaim.status)) {
				return 'OK';
			}
		}

		if (serviceCase) {
			if (['WAITING_FOR_REPAIR', 'WAITING_FOR_RETURN'].includes(serviceCase.status)) {
				return 'OK';
			}
		}

		return 'PENDING';
	}

	async prepareInventoryPrices(inventoryId: Inventory['id'], inventoryItems: InventoryItem[]) {
		const { untestedItems, forSaleItems, serviceItems, deadItems } = organizeInventoryItems(inventoryItems);
		const untestedPrice = untestedItems.reduce((acc, curr) => acc.plus(curr.buyPrice), new Decimal(0));
		const forSalePrice = forSaleItems.reduce((acc, curr) => acc.plus(curr.buyPrice), new Decimal(0));
		const servicePrice = serviceItems.reduce((acc, curr) => acc.plus(curr.buyPrice), new Decimal(0));
		const deadPrice = deadItems.reduce((acc, curr) => acc.plus(curr.buyPrice), new Decimal(0));

		await new InventoryController().update(inventoryId, {
			untestedPrice,
			forSalePrice,
			servicePrice,
			deadPrice,
		});
	}

	listFiles(inventoryId: Inventory['id']) {
		return new FileInventoryController().list(listAll({ filter: { inventoryId: { eq: inventoryId } }, sort: ['sequence'] }));
	}

	async addFiles(inventoryId: Inventory['id'], fileIds: SingleOrArray<File['id'] | { id: File['id'] }>) {
		const [files] = await this.listFiles(inventoryId);
		const maxSequence = files.reduce((acc, file) => Math.max(acc, file.sequence), 0);
		const fileIdsToAdd = (Array.isArray(fileIds) ? fileIds : [fileIds])
			.map((fileId) => (typeof fileId === 'string' ? fileId : fileId.id))
			.filter((fileId) => !files.some((file) => file.fileId === fileId));
		return new FileInventoryController().link(inventoryId, fileIdsToAdd, { sequence: maxSequence + 1 });
	}

	async deleteFile(productId: Inventory['id'], fileId: File['id'] | { id: File['id'] }) {
		return new FileInventoryController().unlink(productId, fileId);
	}

	async prepareExcelReport(inventoryId: Inventory['id'], variant: 'untestedItems' | 'forSaleItems' | 'serviceItems' | 'deadItems') {
		const [inventoryItems] = await new InventoryItemController().list(listAll({ filter: { inventoryId: { eq: inventoryId } } }));
		const selectedItems = organizeInventoryItems(inventoryItems)[variant];
		return generateInventoryReport(selectedItems);
	}
}
