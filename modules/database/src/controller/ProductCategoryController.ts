import { type ProductStatus } from '@pocitarna-nx-2023/config';
import { filterUndefined, type SingleOrArray, uniques } from '@pocitarna-nx-2023/utils';
import type { FindTreeOptions } from 'typeorm';
import * as XLSX from 'xlsx';
import { ProductCategory } from '../entity';
import { type QueryBuilderMiddleware } from '../types';
import { BaseClosureTableController } from '../utils/BaseClosureTableController';
import { listAll } from '../utils/list';
import { AttributeController, ProductCategoryAttributeController } from '.';

const repositoryFindOptions: FindTreeOptions = { relations: ['recyclingFee', 'shoptetCategory'] };

export class ProductCategoryController extends BaseClosureTableController<ProductCategory> {
	constructor() {
		super(ProductCategory);
	}

	protected override getQueryBuilder(...middlewares: QueryBuilderMiddleware<ProductCategory>[]) {
		return super
			.getQueryBuilder(...middlewares)
			.leftJoinAndSelect('entity.shoptetCategory', 'shoptetCategory')
			.leftJoinAndSelect('entity.recyclingFee', 'recyclingFee');
	}

	async findByName(name: ProductCategory['name']) {
		return this.repository.findOne({ where: { name } });
	}

	async findByCodePrefix(codePrefix: ProductCategory['codePrefix']) {
		return this.repository.findOne({ where: { codePrefix } });
	}

	addAttribute(...props: Parameters<ProductCategoryAttributeController['link']>) {
		return new ProductCategoryAttributeController().link(...props);
	}

	removeAttribute(...props: Parameters<ProductCategoryAttributeController['unlink']>) {
		return new ProductCategoryAttributeController().unlink(...props);
	}

	removeAttributes(...props: Parameters<ProductCategoryAttributeController['unlinkAll']>) {
		return new ProductCategoryAttributeController().unlinkAll(...props);
	}

	async findByAttributes(attributes: { id: string }[]) {
		const attributeIds = uniques(attributes.map((att) => att.id));
		if (attributeIds.length === 0) return null;

		const categories = await this.repository.find({ where: { categoryAttributes: attributeIds.map((id) => ({ id })) } });

		if (categories.length === 0) return null;

		return categories[0];
	}

	async findCodePrefixes() {
		const categories = await this.listAll();
		return categories.map((category) => category.codePrefix);
	}

	async getAllFlattenedCategories() {
		const categories = await this.listAll();

		const allCategoriesWithDescendants = filterUndefined(
			await Promise.all(
				categories.map(async (category) => {
					return await this.listDescendantsArray(category.id);
				}),
			),
		);

		return allCategoriesWithDescendants.flat();
	}

	override listAll() {
		return super.listAll(repositoryFindOptions);
	}

	override listDescendantsTree(id: ProductCategory['id']) {
		return super.listDescendantsTree(id, repositoryFindOptions);
	}

	override listDescendantsArray(id: ProductCategory['id']) {
		return super.listDescendantsArray(id, repositoryFindOptions);
	}

	override listAncestorsTree(id: ProductCategory['id']) {
		return super.listAncestorsTree(id, repositoryFindOptions);
	}

	override listAncestorsArray(id: ProductCategory['id']) {
		return super.listAncestorsArray(id, repositoryFindOptions);
	}

	async findByProductStatus(status: SingleOrArray<ProductStatus>, categoryAttributesType = 'envelope') {
		const queryBuilder = this.getQueryBuilder()
			.innerJoin('entity.products', 'product')
			.innerJoinAndSelect('entity.categoryAttributes', 'categoryAttributes');

		return this.listFlat(
			listAll({
				filter: { 'product.status': { eq: status }, 'categoryAttributes.type': { eq: categoryAttributesType } },
				sort: [['categoryAttributes.sequence', 'asc']],
			}),

			queryBuilder,
		);
	}

	async createImportFile(productCategoryId: ProductCategory['id']) {
		const category = await this.findById(productCategoryId);

		if (!category) throw new Error('Category not found');

		const [categoryAttributes] = await new AttributeController().listByCategory(
			productCategoryId,
			listAll({
				filter: {
					'categoryAttributes.type': { eq: 'envelope' },
				},
				sort: ['categoryAttributes.sequence'],
			}),
		);

		const columnNames = [
			...categoryAttributes.map((attribute) => attribute.displayName),
			'Nákupní cena v CZK',
			'Prodejní cena v CZK s DPH',
			'Standardní cena v CZK s DPH',
		];

		const workbook = XLSX.utils.book_new();
		const sheet = XLSX.utils.aoa_to_sheet([columnNames]);
		sheet['!cols'] = columnNames.map(() => ({ wch: 20 }));
		XLSX.utils.book_append_sheet(workbook, sheet, category.name);
		return XLSX.write(workbook, { type: 'buffer', bookType: 'xlsx', compression: true, cellStyles: true }) as Buffer;
	}
}
