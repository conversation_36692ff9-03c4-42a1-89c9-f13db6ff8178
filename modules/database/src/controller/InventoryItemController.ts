import { ORDER_SUCCESS_STATUSES, type ProductStatus } from '@pocitarna-nx-2023/config';
import { type InventoryItemUpdateBody, type ListProps, type ServiceTaskUpdate } from '@pocitarna-nx-2023/zodios';
import {
	type CustomerClaim,
	type EcommerceOrder,
	type Inventory,
	InventoryItem,
	type Product,
	type ProductDefect,
	type ProductEnvelope,
	type ServiceCase,
	type WarrantyClaim,
} from '../entity';
import { type QueryBuilderMiddleware } from '../types';
import { BaseController } from '../utils/BaseController';
import { listAll, listOne } from '../utils/list';
import { CustomerClaimController } from './CustomerClaimController';
import { EcommerceOrderController } from './EcommerceOrderController';
import { EcommerceOrderItemController } from './EcommerceOrderItemController';
import { ProductController } from './ProductController';
import { ProductDefectController } from './ProductDefectController';
import { ServiceCaseController } from './ServiceCaseController';
import { ServiceTaskController } from './ServiceTaskController';
import { VendorController } from './VendorController';
import { WarrantyClaimController } from './WarrantyClaimController';

export class InventoryItemController extends BaseController<InventoryItem> {
	constructor() {
		super(InventoryItem);
	}

	protected includeAttributeValuesMiddleware: QueryBuilderMiddleware<InventoryItem> = (qb) =>
		qb
			.innerJoinAndSelect('entity.product', 'product')
			.innerJoinAndSelect('product.attributeValues', 'productAttributeValue')
			.innerJoinAndSelect('productAttributeValue.attributeValue', 'attributeValue')
			.innerJoinAndSelect('attributeValue.attribute', 'attribute');

	async listWithManufacturer(props: ListProps, queryBuilder = this.getQueryBuilder(this.includeAttributeValuesMiddleware)) {
		return this.list({ ...props, filter: { ...props.filter, 'attribute.displayName': { eq: 'Značka (výrobce)' } } }, queryBuilder);
	}

	async findByCode(inventoryId: InventoryItem['inventoryId'], code: InventoryItem['code'] | InventoryItem['code'][]) {
		if (Array.isArray(code)) {
			const [items] = await this.list(listAll({ filter: { inventoryId: { eq: inventoryId }, code: { eq: code } } }));
			return items;
		}

		const [item] = await this.list(listOne({ filter: { inventoryId: { eq: inventoryId }, code: { eq: code } } }));
		return item;
	}

	async handleInventoryItemAction(
		data: InventoryItemUpdateBody & { inventoryId: Inventory['id']; inventoryItemId: InventoryItem['id'] },
	) {
		if (data.actionType === 'SWAP_PRODUCT_IN_ORDER') {
			const [[inventoryItemToSwapIn]] = await new InventoryItemController().list(
				listOne({ filter: { inventoryId: { eq: data.inventoryId }, productId: { eq: data.productToSwapIn } } }),
			);

			return await this.handleInventoryItemSwapInOrder({
				...data,
				inventoryItemToSwapIn: inventoryItemToSwapIn?.id,
				inventoryItemToSwapOut: data.inventoryItemId,
			});
		}

		if (data.actionType === 'ADD_PRODUCT_TO_ORDER') {
			return await this.handleInventoryItemAdditionToOrder(data);
		}

		if (data.actionType === 'REMOVE_PRODUCT_FROM_ORDER') {
			return await this.handleInventoryItemRemovalFromOrder(data);
		}

		if (data.actionType === 'CREATE_SERVICE_CASE') {
			return await this.handleInventoryItemServiceCaseCreation(data);
		}

		if (data.actionType === 'CREATE_CUSTOMER_CLAIM') {
			return await this.handleInventoryItemCustomerClaimCreation(data);
		}

		if (data.actionType === 'CREATE_WARRANTY_CLAIM') {
			return await this.handleInventoryItemWarrantyClaimCreation(data);
		}

		if (data.actionType === 'RESOLVE_OPEN_TASK') {
			return await this.handleInventoryItemOpenTaskResolution(data);
		}

		if (data.actionType === 'CHANGE_PRODUCT_STATUS') {
			return await this.handleInventoryItemProductStatusChange(data);
		}

		if (data.actionType === 'MARK_PRODUCT_AS_OK') {
			return await this.update(data.inventoryItemId, { inventoryStatus: 'OK' });
		}
	}

	async handleInventoryItemSwapInOrder({
		inventoryItemToSwapOut,
		productToSwapOut,
		productToSwapIn,
		inventoryItemToSwapIn,
		orderToAddTo,
	}: {
		inventoryItemToSwapOut: InventoryItem['id'];
		productToSwapOut: Product['id'];
		productToSwapIn: Product['id'];
		inventoryItemToSwapIn: InventoryItem['id'] | undefined;
		orderToAddTo: EcommerceOrder['id'];
	}) {
		const orderItem = await new EcommerceOrderItemController().findByProduct(productToSwapOut);

		if (!orderItem) throw new Error('Cannot swap order items');

		// Update orderItem
		await new EcommerceOrderItemController().update(orderItem, { product: { id: productToSwapIn } });

		// Update inventoryItem to swap out
		await new InventoryItemController().update(inventoryItemToSwapOut, {
			ecommerceOrder: null,
			ecommerceOrderCode: null,
			status: 'FOR_SALE',
			inventoryStatus: 'OK',
		});

		// Update product to swap out
		await new ProductController().update(productToSwapOut, { status: 'FOR_SALE' });

		if (inventoryItemToSwapIn) {
			const order = await new EcommerceOrderController().findById(orderToAddTo);

			if (!order) throw new Error('Order not found');

			const orderIsCompleted = ORDER_SUCCESS_STATUSES.includes(order.status);

			const newProductStatus = orderIsCompleted ? 'SOLD' : 'RESERVED';

			// Update inventoryItem to swap in
			await new InventoryItemController().update(inventoryItemToSwapIn, {
				ecommerceOrder: { id: orderToAddTo },
				ecommerceOrderCode: order.code,
				status: newProductStatus,
				inventoryStatus: 'OK',
			});

			// Update product to swap in
			await new ProductController().update(productToSwapIn, { status: newProductStatus });
		}
	}

	async handleInventoryItemAdditionToOrder({
		inventoryItemId,
		productToAdd,
		orderToAddTo,
		productEnvelopeId,
	}: {
		inventoryItemId: InventoryItem['id'];
		productToAdd: Product['id'];
		orderToAddTo: EcommerceOrder['id'];
		productEnvelopeId: ProductEnvelope['id'];
	}) {
		const [[orderItemToPair]] = await new EcommerceOrderItemController().list(
			listOne({
				filter: {
					ecommerceOrderId: { eq: orderToAddTo },
					productEnvelopeId: { eq: productEnvelopeId },
					type: { eq: 'PRODUCT' },
					productId: { eq: null },
				},
			}),
		);

		if (!orderItemToPair) throw new Error('Cannot add product to order');

		// Update orderItem
		await new EcommerceOrderItemController().update(orderItemToPair, {
			product: { id: productToAdd },
			productEnvelope: { id: productEnvelopeId },
		});

		const order = await new EcommerceOrderController().findById(orderToAddTo);

		if (!order) throw new Error('Order not found');

		const orderIsCompleted = ORDER_SUCCESS_STATUSES.includes(order.status);

		const newProductStatus = orderIsCompleted ? 'SOLD' : 'RESERVED';

		// Update inventoryItem
		await new InventoryItemController().update(inventoryItemId, {
			ecommerceOrder: { id: orderToAddTo },
			ecommerceOrderCode: order.code,
			inventoryStatus: 'OK',
			status: newProductStatus,
		});

		// Update product
		await new ProductController().update(productToAdd, { status: newProductStatus });
	}

	async handleInventoryItemRemovalFromOrder({
		inventoryItemId,
		productToDelete,
	}: {
		inventoryItemId: InventoryItem['id'];
		productToDelete: Product['id'];
	}) {
		const orderItem = await new EcommerceOrderItemController().findByProduct(productToDelete);

		if (!orderItem) throw new Error('Cannot remove product from order');

		// Update orderItem
		await new EcommerceOrderItemController().update(orderItem, { product: null });
		const newProductStatus = 'FOR_SALE';

		// Update product
		await new ProductController().update(productToDelete, { status: newProductStatus });

		// Update inventoryItem
		await new InventoryItemController().update(inventoryItemId, {
			ecommerceOrder: null,
			ecommerceOrderCode: null,
			status: newProductStatus,
			inventoryStatus: 'OK',
		});
	}

	async handleInventoryItemServiceCaseCreation({
		inventoryItemId,
		serviceCaseId,
	}: {
		inventoryItemId: InventoryItem['id'];
		serviceCaseId: ServiceCase['id'];
	}) {
		const serviceCase = await new ServiceCaseController().findById(serviceCaseId);

		if (!serviceCase) throw new Error('Service case not found');

		await new InventoryItemController().update(inventoryItemId, {
			serviceCase: { id: serviceCase.id },
			serviceCaseCode: serviceCase.code?.code ?? null,
			warrantyClaim: null,
			warrantyClaimCode: null,
			customerClaim: null,
			customerClaimCode: null,
			status: 'SERVICE',
			inventoryStatus: 'OK',
		});
	}

	async handleInventoryItemCustomerClaimCreation({
		inventoryItemId,
		productId,
		productDefectId,
	}: {
		inventoryItemId: InventoryItem['id'];
		productId: Product['id'];
		productDefectId: ProductDefect['id'];
	}) {
		const productDefect = await new ProductDefectController().findById(productDefectId);

		if (!productDefect) throw new Error('Cannot create customer claim');

		const customerClaim = await new CustomerClaimController().create({
			note: productDefect.note ?? '',
			productDefects: [productDefect],
			productId,
		});

		await new InventoryItemController().update(inventoryItemId, {
			customerClaim: { id: customerClaim.id },
			customerClaimCode: customerClaim.code?.code ?? null,
			serviceCase: null,
			serviceCaseCode: null,
			warrantyClaim: null,
			warrantyClaimCode: null,
			status: 'CUSTOMER_CLAIM',
			inventoryStatus: 'OK',
		});
	}

	async handleInventoryItemWarrantyClaimCreation({
		inventoryItemId,
		productId,
		productDefectId,
	}: {
		inventoryItemId: InventoryItem['id'];
		productId: Product['id'];
		productDefectId: ProductDefect['id'];
	}) {
		const batch = await new ProductController().getBatch(productId);
		const vendor = batch ? await new VendorController().findById(batch.vendorId ?? '') : null;

		const productDefect = await new ProductDefectController().findById(productDefectId);
		if (!productDefect) throw new Error('Cannot create warranty claim');

		const isDefectCoveredByVendor = (vendor?.defectTypes ?? [])?.every(
			({ defectType }) => defectType.id !== productDefect.defectTypeId,
		);

		if (isDefectCoveredByVendor) {
			const claim = await new WarrantyClaimController().create({
				note: 'Založeno z inventury',
				productDefects: [productDefect],
				productId,
			});

			await new InventoryItemController().update(inventoryItemId, {
				warrantyClaim: { id: claim.id },
				warrantyClaimCode: claim.code?.code ?? null,
				serviceCase: null,
				serviceCaseCode: null,
				customerClaim: null,
				customerClaimCode: null,
				status: 'WARRANTY_CLAIM',
				inventoryStatus: 'OK',
			});
		} else {
			const serviceCase = await new ServiceCaseController().create({
				note: 'Založeno z inventury',
				productDefects: [productDefect],
				productId,
				type: 'FRONTEND',
			});

			await new InventoryItemController().update(inventoryItemId, {
				serviceCase: { id: serviceCase.id },
				serviceCaseCode: serviceCase.code?.code ?? null,
				warrantyClaim: null,
				warrantyClaimCode: null,
				customerClaim: null,
				customerClaimCode: null,
				status: 'SERVICE',
				inventoryStatus: 'OK',
			});
		}
	}

	async handleInventoryItemOpenTaskResolution({
		inventoryItemId,
		productId,
		productDefectsIds,
		serviceTaskTypeId,
		attributeValues,
		price,
		warrantyClaimId,
		serviceCaseId,
		customerClaimId,
	}: Omit<ServiceTaskUpdate, 'status' | 'note' | 'priceType'> & {
		inventoryItemId: InventoryItem['id'];
		productId: Product['id'];
		warrantyClaimId: WarrantyClaim['id'] | null;
		serviceCaseId: ServiceCase['id'] | null;
		customerClaimId: CustomerClaim['id'] | null;
	}) {
		await new ServiceTaskController().createInBulk({
			productDefectsIds,
			serviceTaskTypeId,
			attributeValues,
			price,
			status: 'CLOSED',
			resolutionScope: 'full',
			priceType: customerClaimId ? 'customerClaim' : 'service',
			note: 'Založeno z inventury',
		});

		let newProductStatus: ProductStatus = 'STOCK';

		if (customerClaimId) {
			const order = await new EcommerceOrderController().findByProduct(productId);
			const orderIsCompleted = order && ORDER_SUCCESS_STATUSES.includes(order.status);
			newProductStatus = orderIsCompleted ? 'SOLD' : 'RESERVED';
		}

		if (warrantyClaimId) {
			await new WarrantyClaimController().update(warrantyClaimId, { status: 'CLOSED' });
		}

		if (serviceCaseId) {
			await new ServiceCaseController().update(serviceCaseId, { status: 'CLOSED' });
		}

		if (customerClaimId) {
			await new CustomerClaimController().update(customerClaimId, { status: 'CLOSED', productId });
		}

		await new InventoryItemController().update(inventoryItemId, {
			status: newProductStatus,
			inventoryStatus: 'OK',
			serviceCase: null,
			serviceCaseCode: null,
			customerClaim: null,
			customerClaimCode: null,
			warrantyClaim: null,
			warrantyClaimCode: null,
		});

		await new ProductController().update(productId, { status: newProductStatus });
	}

	async handleInventoryItemProductStatusChange({
		inventoryItemId,
		productId,
		status,
	}: {
		inventoryItemId: InventoryItem['id'];
		productId: Product['id'];
		status: ProductStatus;
	}) {
		await new ProductController().update(productId, { status });
		await new InventoryItemController().update(inventoryItemId, { status, inventoryStatus: 'OK' });
	}
}
