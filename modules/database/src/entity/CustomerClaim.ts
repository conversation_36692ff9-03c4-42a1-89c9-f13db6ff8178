import { CUSTOMER_CLAIM_STATUS_NAMES, type CustomerClaimStatus } from '@pocitarna-nx-2023/config';
import { Column, Entity, Index, ManyToOne, OneToMany } from 'typeorm';
import { CommonEntity } from '../utils/CommonEntity';
import { CustomerClaimCode } from './CustomerClaimCode';
import { InventoryItem } from './InventoryItem';
import { Note } from './Note';
import { ProductDefect } from './ProductDefect';
import { User } from './User';

@Entity({ name: 'customerClaim' })
export class CustomerClaim extends CommonEntity {
	@ManyToOne(() => User, (user) => user.createdCustomerClaims)
	@Index()
	createdBy: User | null;
	@Column({ type: 'uuid', nullable: true })
	createdById: string | null;

	@Column({ type: 'enum', enum: CUSTOMER_CLAIM_STATUS_NAMES, enumName: 'CUSTOMER_CLAIM_STATUS_NAMES', default: 'NEW' })
	@Index()
	status: CustomerClaimStatus;

	@OneToMany(() => ProductDefect, (productDefect) => productDefect.customerClaim)
	productDefects: ProductDefect[];

	@ManyToOne(() => CustomerClaimCode, (code) => code.customerClaim, { onDelete: 'CASCADE' })
	@Index()
	code: CustomerClaimCode;
	@Column({ type: 'uuid' })
	codeId: string;

	@OneToMany(() => Note, (note) => note.customerClaim)
	notes: Note[];

	@OneToMany(() => InventoryItem, (inventoryItem) => inventoryItem.customerClaim)
	inventoryItems: InventoryItem[];
}

// Synchronization is off, because it has to be copied without constraints
@Entity({ name: 'customerClaimHistory', synchronize: false })
export class CustomerClaimHistory extends CustomerClaim {}
