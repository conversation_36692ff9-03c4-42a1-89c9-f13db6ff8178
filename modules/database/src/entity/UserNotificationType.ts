import { NOTIFICATION_DELIVERY_METHODS_NAMES, type NotificationDeliveryMethod } from '@pocitarna-nx-2023/config';
import { Column, Entity, Index, ManyToOne } from 'typeorm';
import { CommonEntity } from '../utils/CommonEntity';
import { NotificationType } from './NotificationType';
import { User } from './User';

@Entity({ name: 'userNotificationType' })
@Index(['notificationType', 'user', 'deliveryMethod'], { unique: true })
export class UserNotificationType extends CommonEntity {
	@Column({ type: 'enum', enum: NOTIFICATION_DELIVERY_METHODS_NAMES, enumName: 'NOTIFICATION_DELIVERY_METHODS_NAMES' })
	@Index()
	deliveryMethod: NotificationDeliveryMethod;

	@ManyToOne(() => NotificationType, (notificationType) => notificationType.users)
	@Index()
	notificationType: NotificationType;
	@Column({ type: 'uuid' })
	notificationTypeId: string;

	@ManyToOne(() => User, (user) => user.notificationTypes)
	@Index()
	user: User;
	@Column({ type: 'uuid' })
	userId: string;
}
