import { DOMAIN_NAME, LAMBDA_ENV, type Stage, TIMEZONE } from '@pocitarna-nx-2023/config';
import { Duration, Stack, type StackProps } from 'aws-cdk-lib';
import { SubnetType, type Vpc } from 'aws-cdk-lib/aws-ec2';
import { ManagedPolicy, Role, ServicePrincipal } from 'aws-cdk-lib/aws-iam';
import { Architecture, type LayerVersion, Runtime } from 'aws-cdk-lib/aws-lambda';
import { SqsEventSource } from 'aws-cdk-lib/aws-lambda-event-sources';
import { NodejsFunction } from 'aws-cdk-lib/aws-lambda-nodejs';
import { RetentionDays } from 'aws-cdk-lib/aws-logs';
import { type Bucket } from 'aws-cdk-lib/aws-s3';
import { CfnSchedule } from 'aws-cdk-lib/aws-scheduler';
import type { Secret } from 'aws-cdk-lib/aws-secretsmanager';
import { type Queue } from 'aws-cdk-lib/aws-sqs';
import { NagSuppressions } from 'cdk-nag';
import { type Construct } from 'constructs';
import { join } from 'path';
import { bundling } from './utils';

type Props = StackProps & {
	stage: Stage;
	vpc: Vpc;
	databaseCredentials: Secret;
	databaseLayer: LayerVersion;
	productPricesSyncDeadLetterQueue: Queue;
	productPricesSyncQueue: Queue;
	filesBucket: Bucket;
	emailBucket: Bucket;
};

export class Housekeeping extends Stack {
	constructor(scope: Construct, id: string, props: Props) {
		super(scope, id, props);

		this.createProductPricesSyncLambda(props);
		this.createBatchReportLambda(props);
		this.createWarehousePickupNotifyLambda(props);
		this.createWarehousePickupReportLambda(props);
		this.createWarehousePickupSyncLambda(props);
		this.createUnreadNotificationsLambda(props);

		NagSuppressions.addStackSuppressions(this, [
			{ id: 'AwsSolutions-IAM4', reason: 'Using managed policies' },
			{ id: 'AwsSolutions-IAM5', reason: 'Using managed policies' },
		]);
	}

	private createRole(name: string) {
		return new Role(this, `${name}ExecutionRole`, {
			assumedBy: new ServicePrincipal('lambda.amazonaws.com'),
			managedPolicies: [
				ManagedPolicy.fromAwsManagedPolicyName('service-role/AWSLambdaBasicExecutionRole'),
				ManagedPolicy.fromAwsManagedPolicyName('service-role/AWSLambdaVPCAccessExecutionRole'),
			],
		});
	}

	private createSchedule(handler: NodejsFunction, name: string, scheduleExpression: string) {
		const role = new Role(this, `${name}ScheduleInvocationRole`, {
			managedPolicies: [ManagedPolicy.fromAwsManagedPolicyName('service-role/AWSLambdaRole')],
			assumedBy: new ServicePrincipal('scheduler.amazonaws.com'),
		});

		return new CfnSchedule(this, `${name}Schedule`, {
			flexibleTimeWindow: { mode: 'OFF' },
			scheduleExpression,
			scheduleExpressionTimezone: TIMEZONE,
			target: {
				arn: handler.functionArn,
				roleArn: role.roleArn,
				retryPolicy: {
					maximumRetryAttempts: 0,
				},
			},
		});
	}

	private createProductPricesSyncLambda({
		vpc,
		stage,
		databaseLayer,
		databaseCredentials,
		productPricesSyncQueue,
		productPricesSyncDeadLetterQueue,
		emailBucket,
	}: Props) {
		const role = this.createRole('ProductPricesSync');
		emailBucket.grantReadWrite(role);

		const handler = new NodejsFunction(this, 'ProductPricesSyncHandler', {
			runtime: Runtime.NODEJS_22_X,
			architecture: Architecture.ARM_64,
			logRetention: RetentionDays.ONE_WEEK,
			entry: join(__dirname, '..', '..', '..', '..', 'apps', 'housekeeping', 'src', 'productPricesSync', 'worker.ts'),
			memorySize: 512,
			role,
			timeout: Duration.minutes(15),
			vpc,
			vpcSubnets: { subnetType: SubnetType.PRIVATE_WITH_EGRESS },
			layers: [databaseLayer],
			retryAttempts: 2,
			environment: {
				...LAMBDA_ENV,
				STAGE: stage,
				DB_PASS: databaseCredentials.secretValue.unsafeUnwrap(),
				DB_HOST: `db.${stage}.${DOMAIN_NAME}`,
			},
			bundling,
		});

		handler.addEventSource(
			new SqsEventSource(productPricesSyncQueue, {
				batchSize: 10,
				maxBatchingWindow: Duration.minutes(1),
				reportBatchItemFailures: true,
			}),
		);

		const dlqHandler = new NodejsFunction(this, 'ProductPricesSyncDLQHandler', {
			runtime: Runtime.NODEJS_22_X,
			architecture: Architecture.ARM_64,
			logRetention: RetentionDays.ONE_WEEK,
			entry: join(__dirname, '..', '..', '..', '..', 'apps', 'housekeeping', 'src', 'productPricesSync', 'dlq.ts'),
			memorySize: 128,
			role,
			timeout: Duration.minutes(15),
			vpc,
			vpcSubnets: { subnetType: SubnetType.PRIVATE_WITH_EGRESS },
			layers: [databaseLayer],
			retryAttempts: 2,
			environment: {
				...LAMBDA_ENV,
				STAGE: stage,
				DB_PASS: databaseCredentials.secretValue.unsafeUnwrap(),
				DB_HOST: `db.${stage}.${DOMAIN_NAME}`,
			},
			bundling,
		});

		dlqHandler.addEventSource(
			new SqsEventSource(productPricesSyncDeadLetterQueue, {
				batchSize: 1,
				reportBatchItemFailures: true,
			}),
		);

		return handler;
	}

	private createBatchReportLambda({ vpc, stage, databaseLayer, databaseCredentials, emailBucket, filesBucket }: Props) {
		const role = this.createRole('OrderSync');
		emailBucket.grantReadWrite(role);
		filesBucket.grantReadWrite(role);

		const handler = new NodejsFunction(this, 'BatchReportHandler', {
			runtime: Runtime.NODEJS_22_X,
			architecture: Architecture.ARM_64,
			logRetention: RetentionDays.ONE_WEEK,
			entry: join(__dirname, '..', '..', '..', '..', 'apps', 'housekeeping', 'src', 'batchReport', 'worker.ts'),
			memorySize: 512,
			reservedConcurrentExecutions: 1,
			role,
			timeout: Duration.minutes(15),
			vpc,
			vpcSubnets: { subnetType: SubnetType.PRIVATE_WITH_EGRESS },
			layers: [databaseLayer],
			retryAttempts: 0,
			environment: {
				...LAMBDA_ENV,
				STAGE: stage,
				DB_PASS: databaseCredentials.secretValue.unsafeUnwrap(),
				DB_HOST: `db.${stage}.${DOMAIN_NAME}`,
			},
			bundling,
		});

		this.createSchedule(handler, 'BatchReport', 'cron(0 * * * ? *)'); // Every hour

		return handler;
	}

	private createWarehousePickupNotifyLambda({ vpc, stage, databaseLayer, databaseCredentials }: Props) {
		const role = this.createRole('WarehousePickupNotify');

		const handler = new NodejsFunction(this, 'WarehousePickupNotifyHandler', {
			runtime: Runtime.NODEJS_22_X,
			architecture: Architecture.ARM_64,
			logRetention: RetentionDays.ONE_WEEK,
			entry: join(__dirname, '..', '..', '..', '..', 'apps', 'housekeeping', 'src', 'warehousePickup', 'notify.ts'),
			memorySize: 512,
			reservedConcurrentExecutions: 1,
			role,
			timeout: Duration.minutes(15),
			vpc,
			vpcSubnets: { subnetType: SubnetType.PRIVATE_WITH_EGRESS },
			layers: [databaseLayer],
			retryAttempts: 0,
			environment: {
				...LAMBDA_ENV,
				STAGE: stage,
				DB_PASS: databaseCredentials.secretValue.unsafeUnwrap(),
				DB_HOST: `db.${stage}.${DOMAIN_NAME}`,
			},
			bundling,
		});

		if (stage === 'prod') {
			this.createSchedule(handler, 'WarehousePickupNotify', 'cron(*/5 * * * ? *)'); // Every 5 minutes
		}

		return handler;
	}

	private createWarehousePickupReportLambda({ vpc, stage, databaseLayer, databaseCredentials, emailBucket }: Props) {
		const role = this.createRole('WarehousePickupReport');
		emailBucket.grantReadWrite(role);

		const handler = new NodejsFunction(this, 'WarehousePickupReportHandler', {
			runtime: Runtime.NODEJS_22_X,
			architecture: Architecture.ARM_64,
			logRetention: RetentionDays.ONE_WEEK,
			entry: join(__dirname, '..', '..', '..', '..', 'apps', 'housekeeping', 'src', 'warehousePickup', 'report.ts'),
			memorySize: 512,
			reservedConcurrentExecutions: 1,
			role,
			timeout: Duration.minutes(15),
			vpc,
			vpcSubnets: { subnetType: SubnetType.PRIVATE_WITH_EGRESS },
			layers: [databaseLayer],
			retryAttempts: 0,
			environment: {
				...LAMBDA_ENV,
				STAGE: stage,
				DB_PASS: databaseCredentials.secretValue.unsafeUnwrap(),
				DB_HOST: `db.${stage}.${DOMAIN_NAME}`,
			},
			bundling,
		});

		if (stage === 'prod') {
			this.createSchedule(handler, 'WarehousePickupReport', 'cron(0 2 * * ? *)'); // Nightly
		}

		return handler;
	}

	private createWarehousePickupSyncLambda({ vpc, stage, databaseLayer, databaseCredentials }: Props) {
		const role = this.createRole('WarehousePickupSync');

		const handler = new NodejsFunction(this, 'WarehousePickupSyncHandler', {
			runtime: Runtime.NODEJS_22_X,
			architecture: Architecture.ARM_64,
			logRetention: RetentionDays.ONE_WEEK,
			entry: join(__dirname, '..', '..', '..', '..', 'apps', 'housekeeping', 'src', 'warehousePickup', 'sync.ts'),
			memorySize: 512,
			reservedConcurrentExecutions: 1,
			role,
			timeout: Duration.minutes(15),
			vpc,
			vpcSubnets: { subnetType: SubnetType.PRIVATE_WITH_EGRESS },
			layers: [databaseLayer],
			retryAttempts: 0,
			environment: {
				...LAMBDA_ENV,
				STAGE: stage,
				DB_PASS: databaseCredentials.secretValue.unsafeUnwrap(),
				DB_HOST: `db.${stage}.${DOMAIN_NAME}`,
			},
			bundling,
		});

		// Invoked only manually

		return handler;
	}

	private createUnreadNotificationsLambda({ vpc, stage }: Props) {
		const role = this.createRole('UnreadNotifications');

		const handler = new NodejsFunction(this, 'UnreadNotificationsHandler', {
			runtime: Runtime.NODEJS_22_X,
			architecture: Architecture.ARM_64,
			logRetention: RetentionDays.ONE_WEEK,
			entry: join(__dirname, '..', '..', '..', '..', 'apps', 'housekeeping', 'src', 'unreadNotifications', 'worker.ts'),
			memorySize: 512,
			reservedConcurrentExecutions: 1,
			role,
			timeout: Duration.minutes(15),
			vpc,
			vpcSubnets: { subnetType: SubnetType.PRIVATE_WITH_EGRESS },
			retryAttempts: 0,
			environment: {
				...LAMBDA_ENV,
				STAGE: stage,
				NEXT_PUBLIC_API_ORIGIN: `https://api.${stage}.${DOMAIN_NAME}`,
			},
			bundling,
		});

		this.createSchedule(handler, 'UnreadNotifications', 'cron(0 * * * ? *)'); // Every hour

		return handler;
	}
}
