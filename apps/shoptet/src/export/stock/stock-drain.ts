import { gunzipSync } from 'node:zlib';
import { triggerEmail, withSentry, withSqsRecord } from '@pocitarna-nx-2023/aws';
import { DOMAIN_NAME, MATEJ_EMAIL, PETR_EMAIL, SHOPTET_ADMIN_URL } from '@pocitarna-nx-2023/config';
import { listAll, ProductEnvelopeController, withDatabase } from '@pocitarna-nx-2023/database';
import { createShoptetClient, type Product as ShoptetProduct } from '@pocitarna-nx-2023/shoptet-client';
import { formatEnvelopeCode, formatSheetForEmail, stripCode } from '@pocitarna-nx-2023/utils';
import * as XLSX from 'xlsx';

const shoptetClient = createShoptetClient('CZ');

export const handler = withSentry(
	'https://<EMAIL>/4507848018690128',
	withDatabase(
		withSqsRecord(async (record) => {
			const jobResponse = await shoptetClient.job.getJobDetail(record.body);
			const jobStatus = jobResponse.data.data.job.status;
			if (jobStatus !== 'completed') throw new Error(`Job ${record.body} is not completed yet!`);

			const url = jobResponse.data.data.job.resultUrl;
			const dataResponse = await fetch(url);
			const data = await dataResponse.arrayBuffer();
			const decompressedData = gunzipSync(data);
			const productDetails = decompressedData
				.toString('utf-8')
				.trim()
				.split('\n')
				.filter((line) => line.trim())
				.map((line) => JSON.parse(line) as ShoptetProduct);

			const transformedProductDetails = productDetails.reduce<Record<number, { url: string; amount: number }>>((acc, product) => {
				const productVariant = product.variants.at(0);
				if (!productVariant) return acc;

				acc[stripCode(productVariant.code)] = {
					url: `${SHOPTET_ADMIN_URL}/produkty-detail/?guid=${product.guid}`,
					amount:
						(productVariant.perStockAmounts as { amount: string }[] | undefined)?.reduce(
							(acc, stock) => acc + Number(stock.amount),
							0,
						) ?? 0,
				};

				return acc;
			}, {});

			const [envelopes] = await new ProductEnvelopeController().listWithProductCount(
				listAll({ filter: { 'code.code': { eq: Object.keys(transformedProductDetails) } } }),
			);

			const differences = envelopes
				.toSorted((a, b) => a.code.code - b.code.code)
				.reduce<{ code: string; shoptetStock: number; cmpStock: number; cmpUrl: string; shoptetUrl: string }[]>((acc, envelope) => {
					const { amount: shoptetStock, url: shoptetUrl } = transformedProductDetails[envelope.code.code];
					const cmpStock = envelope.productCount ?? 0;
					const cmpUrl = `https://cmp.${DOMAIN_NAME}/product/envelope/${envelope.id}`;

					const code = formatEnvelopeCode(envelope.productCategory.codePrefix)(envelope.code);

					if (shoptetStock !== cmpStock) {
						acc.push({ code, shoptetStock, cmpStock, cmpUrl, shoptetUrl });

						// TODO - export lower value to shoptet
					}

					return acc;
				}, []);

			if (differences.length > 0) {
				const emailSheet = XLSX.utils.json_to_sheet(differences);

				await triggerEmail({
					recipients: [MATEJ_EMAIL, PETR_EMAIL],
					subject: 'Nesoulad v kontrole skladů',
					message: formatSheetForEmail(emailSheet),
				});
			}
		}),
	),
);
