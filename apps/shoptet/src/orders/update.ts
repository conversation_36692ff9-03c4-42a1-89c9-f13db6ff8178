import { withSentry, withSqsRecord } from '@pocitarna-nx-2023/aws';
import { EcommerceOrderController, withDatabase } from '@pocitarna-nx-2023/database';
import { createShoptetClient } from '@pocitarna-nx-2023/shoptet-client';
import { fetchOrderDetail, handleOrdersWarehouseTasks, updateOrder } from './utils';

export const handler = withSentry(
	'https://<EMAIL>/4507848018690128',
	withDatabase(
		withSqsRecord(async (record) => {
			const order = await new EcommerceOrderController().findById(record.body);
			if (!order) return;
			const client = createShoptetClient(order.country);
			const shoptetOrder = await fetchOrderDetail(order.code, client);
			if (!shoptetOrder) return;

			await updateOrder(order, shoptetOrder, order.country);
			await handleOrdersWarehouseTasks(shoptetOrder);
		}),
	),
);
