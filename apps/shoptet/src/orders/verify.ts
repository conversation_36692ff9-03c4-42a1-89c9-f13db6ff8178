import { triggerEmail, withSentry } from '@pocitarna-nx-2023/aws';
import { SHOPTET_COUNTRIES, SHOPTET_ORDERS_LAMBDA_ENDPOINT } from '@pocitarna-nx-2023/config';
import { withAction, withDatabase } from '@pocitarna-nx-2023/database';
import { AxiosError } from 'axios';
import { sub } from 'date-fns';
import { importShoptetOrders } from './utils';

export const handler = withSentry(
	'https://<EMAIL>/4507848018690128',
	withDatabase(
		withAction(SHOPTET_ORDERS_LAMBDA_ENDPOINT, async () => {
			const date = sub(new Date(), { weeks: 1 }); // Week ago

			try {
				for (const shoptetCountry of SHOPTET_COUNTRIES) {
					// eslint-disable-next-line no-console
					console.log(`Importing orders for country: ${shoptetCountry}`);
					await importShoptetOrders(shoptetCountry, date);
				}
			} catch (error) {
				console.error(error);
				if (error instanceof AxiosError) {
					await triggerEmail({
						recipients: '<EMAIL>',
						subject: 'Shoptet order verify failed',
						message: JSON.stringify(error.response?.data ?? {}),
					});
				}
			}
		}),
	),
);
