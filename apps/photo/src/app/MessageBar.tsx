import { type FC, type PropsWithChildren } from 'react';
import { StyleSheet, Text, View } from 'react-native';
import { COLORS } from './globalStyles';

type Props = PropsWithChildren<{
	error?: boolean;
	zIndex?: number;
}>;

export const MessageBar: FC<Props> = ({ error, children, zIndex = 1 }) => {
	return (
		<View style={styles.view}>
			<Text style={[styles.text, error ? styles.textError : styles.textDefault, { zIndex }]}>{children}</Text>
		</View>
	);
};
const styles = StyleSheet.create({
	view: {
		position: 'absolute',
		bottom: 0,
		right: 0,
		left: 0,
		padding: 5,
		backgroundColor: COLORS.white,
	},
	text: {
		textAlign: 'center',
	},
	textDefault: {
		color: COLORS.text,
	},
	textError: {
		color: COLORS.error,
		fontWeight: 'bold',
	},
});
