import { MAX_POSITIVE_INTEGER, type PHOTO_USAGE, TEN_SECONDS } from '@pocitarna-nx-2023/config';
import { But<PERSON>, Stack } from '@pocitarna-nx-2023/ui';
import { apiHooks } from '@pocitarna-nx-2023/zodios-client';
import { type FC, useEffect, useState } from 'react';
import { useFormContext } from 'react-hook-form';
import { FileUploader } from './FileUploader';
import { TemporaryFiles } from './TemporaryFiles';

type Props = {
	inputName: string;
	pageType: keyof typeof PHOTO_USAGE;
	entityCode: string;
	isUploadingForm: boolean;
	isUploadingFile: boolean;
	toggleUploadingFile: () => void;
	hideSubmitButton?: boolean;
	label?: string;
	disabled?: boolean;
};

// This component is used to create files before they are actually linked to a specific entity (e.g. adding images to a product defect before it gets created)
export const TemporaryFileUploader: FC<Props> = ({
	inputName,
	label,
	pageType,
	entityCode,
	isUploadingForm,
	isUploadingFile,
	toggleUploadingFile,
	hideSubmitButton,
	disabled = false,
}) => {
	const [photoSessionId, setPhotoSessionId] = useState<string | null>(null);
	const [fileValues, setFileValues] = useState<string[]>([]);
	const [removedTemporaryFiles, setRemovedTemporaryFiles] = useState<string[]>([]);
	const [afterInitialLoad, setAfterInitialLoad] = useState(false);

	const { setValue } = useFormContext();

	const uploadActive = Boolean(photoSessionId) || fileValues.length > 0;

	const { data: temporaryFilesData } = apiHooks.useGetUploadedFiles(
		{ queries: { photoSessionId: photoSessionId ?? undefined, photoIds: fileValues, page: 1, limit: MAX_POSITIVE_INTEGER } },
		{
			enabled: uploadActive,
			refetchInterval: TEN_SECONDS,
		},
	);

	useEffect(() => {
		setAfterInitialLoad(true);
		if (uploadActive) {
			const newFileIds = (temporaryFilesData?._data ?? [])
				.filter((file) => !fileValues.includes(file.id) && !removedTemporaryFiles.includes(file.id))
				.map((file) => file.id);

			setFileValues([...fileValues, ...newFileIds]);
		}
		// eslint-disable-next-line react-hooks/exhaustive-deps
	}, [temporaryFilesData?._data]);

	useEffect(() => {
		if (!isUploadingForm) {
			setPhotoSessionId(null);
			setAfterInitialLoad(false);
			setFileValues([]);
			setRemovedTemporaryFiles([]);
		}
	}, [isUploadingForm]);

	useEffect(() => {
		setValue(inputName, fileValues);
		// eslint-disable-next-line react-hooks/exhaustive-deps
	}, [fileValues]);

	const handleRemoveTemporaryFile = (fileId: string) => {
		setFileValues(fileValues.filter((file: string) => file !== fileId));

		setRemovedTemporaryFiles((files) => [...files, fileId]);
	};

	const temporaryFiles = temporaryFilesData?._data ?? [];
	const shownTemporaryFiles = temporaryFiles.filter((file) => !removedTemporaryFiles.includes(file.id));

	return (
		<Stack gap={4} className="grow">
			<FileUploader
				inputName={inputName}
				label={label}
				pageType={pageType}
				entityCode={entityCode}
				isAddingFiles={isUploadingFile}
				toggleUploadingFile={toggleUploadingFile}
				addFilesToEntity={(newIds) => setFileValues((prev) => [...prev, ...newIds])}
				photoSessionId={photoSessionId}
				setPhotoSessionId={setPhotoSessionId}
				disabled={disabled}
			/>
			{shownTemporaryFiles.length > 0 && uploadActive && afterInitialLoad && (
				<>
					<TemporaryFiles files={shownTemporaryFiles} handleRemoveTemporaryFile={handleRemoveTemporaryFile} />
					{!hideSubmitButton && (
						<Button type="submit" disabled={isUploadingFile || disabled}>
							Nahrát
						</Button>
					)}
				</>
			)}
		</Stack>
	);
};
