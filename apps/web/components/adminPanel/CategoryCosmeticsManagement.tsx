import { MAX_POSITIVE_INTEGER, NOT_AVAILABLE, REQUIRED_FIELD } from '@pocitarna-nx-2023/config';
import {
	<PERSON><PERSON>,
	<PERSON><PERSON>,
	<PERSON>,
	CardContent,
	<PERSON><PERSON><PERSON>er,
	Card<PERSON>eader,
	CardTitle,
	Dialog,
	<PERSON>alogContent,
	DialogFooter,
	DialogHeader,
	DialogTitle,
	DialogTrigger,
	FormContext,
	Icon,
	InputControl,
	SidebarGrid,
	Stack,
	toast,
	useDialog,
} from '@pocitarna-nx-2023/ui';
import { type ApiBody } from '@pocitarna-nx-2023/zodios';
import { apiHooks } from '@pocitarna-nx-2023/zodios-client';
import { groupBy } from 'rambdax';
import { type FC, useMemo } from 'react';
import { z } from 'zod';
import { AddOrEditCosmeticDefect } from './AddOrEditCosmeticDefect';
import { CosmeticDefectListTable } from './CosmeticDefectListTable';

type Props = {
	productCategoryId: string;
};

export const CategoryCosmeticsManagement: FC<Props> = ({ productCategoryId }) => {
	const { data: categoryCosmeticAreasData, invalidate } = apiHooks.useGetCosmeticAreas({
		queries: {
			page: 1,
			limit: MAX_POSITIVE_INTEGER,
			filter: { productCategoryId: { eq: productCategoryId } },
			sort: [['name', 'asc']],
		},
	});

	const { data: categoryCosmeticDefectsData } = apiHooks.useGetCosmeticDefects({
		queries: {
			page: 1,
			limit: MAX_POSITIVE_INTEGER,
			filter: { 'productCategoryCosmeticDefects.productCategoryId': { eq: productCategoryId } },
		},
	});

	const categoryCosmeticDefects = useMemo(() => categoryCosmeticDefectsData?._data ?? [], [categoryCosmeticDefectsData?._data]);

	const groupedByArea = useMemo(() => {
		return groupBy(
			(defect) => defect.cosmeticAreaCosmeticDefects?.[0]?.cosmeticAreaId as string,
			categoryCosmeticDefects.filter((defect) => defect.cosmeticAreaCosmeticDefects?.length),
		);
	}, [categoryCosmeticDefects]);

	const categoryCosmeticAreas = useMemo(() => categoryCosmeticAreasData?._data ?? [], [categoryCosmeticAreasData?._data]);

	return (
		<>
			<div className="flex justify-between items-center max-md:flex-col max-md:items-stretch gap-4">
				<h2 className="h1">Kosmetické vady</h2>
				<Dialog>
					<DialogTrigger asChild>
						<Button variant="secondary">
							<Icon name="add" />
							Vytvořit kosmetickou vadu
						</Button>
					</DialogTrigger>
					<DialogContent>
						<DialogHeader>
							<DialogTitle>Vytvořit kosmetickou vadu</DialogTitle>
						</DialogHeader>
						<AddOrEditCosmeticDefect />
					</DialogContent>
				</Dialog>
			</div>
			<SidebarGrid>
				<Stack gap={4}>
					{Object.values(groupedByArea).map((group) => (
						<Card key={group[0].cosmeticAreaCosmeticDefects?.[0]?.cosmeticAreaId}>
							<CardHeader>
								<CardTitle>
									Vady pro lokalitu &quot;{group[0].cosmeticAreaCosmeticDefects?.[0]?.cosmeticArea.name ?? NOT_AVAILABLE}
									&quot;
								</CardTitle>
							</CardHeader>
							<CosmeticDefectListTable cosmeticDefects={group} inCard />
						</Card>
					))}
				</Stack>

				<Card>
					<CardHeader className="flex flex-col justify-between items-start md:flex-row">
						<CardTitle>Lokality závad</CardTitle>
					</CardHeader>
					<CardContent>
						<Stack direction="row" gap={4} className="items-center flex-wrap">
							{categoryCosmeticAreas.map((area) => (
								<CosmeticArea key={area.id} area={area} invalidate={invalidate} />
							))}
						</Stack>
					</CardContent>
					<CardFooter>
						<Dialog>
							<DialogTrigger asChild>
								<Button className="w-full" variant="secondary">
									<Icon name="plus" />
									Přidat lokalitu závad
								</Button>
							</DialogTrigger>
							<DialogContent>
								<DialogHeader>
									<DialogTitle>Přidat lokalitu závad</DialogTitle>
								</DialogHeader>
								<CosmeticAreaCreation productCategoryId={productCategoryId} invalidate={invalidate} />
							</DialogContent>
						</Dialog>
					</CardFooter>
				</Card>
			</SidebarGrid>
		</>
	);
};

const CosmeticArea: FC<{ area: ApiBody<'getCosmeticAreas'>[number]; invalidate: () => Promise<void> }> = ({ area, invalidate }) => {
	const { mutate: deleteCosmeticArea } = apiHooks.useDeleteCosmeticArea(
		{
			params: { cosmeticAreaId: area.id },
		},
		{
			onSuccess: () => {
				invalidate();
				toast.success('Lokalita závad byla smazána.');
			},
		},
	);

	return (
		<Badge
			variant="secondary"
			className="line-clamp-1 font-normal px-2 cursor-pointer hover:opacity-80"
			onClick={(e) => {
				e.stopPropagation();
				deleteCosmeticArea(undefined);
			}}
		>
			<div className="flex items-center">
				{area.name}
				<Icon name="xmark" className="ml-1 -mr-1 shrink-0" />
			</div>
		</Badge>
	);
};

const CosmeticAreaCreation: FC<{ productCategoryId: string; invalidate: () => Promise<void> }> = ({ productCategoryId, invalidate }) => {
	const { closeDialog } = useDialog();
	const { mutate, isLoading } = apiHooks.useCreateCosmeticArea(undefined);

	return (
		<FormContext
			schema={z.object({
				name: z.string().min(1, { message: REQUIRED_FIELD }),
			})}
			defaultValues={{
				name: '',
			}}
			onSubmit={(data) => {
				mutate(
					{ ...data, productCategory: { id: productCategoryId } },
					{
						onSuccess: () => {
							invalidate();
							closeDialog();
						},
					},
				);
			}}
		>
			{(control) => (
				<Stack gap={4}>
					<InputControl control={control} name="name" label="Název" />

					<DialogFooter>
						<Button className="w-full" variant="default" type="submit" isLoading={isLoading}>
							Vytvořit
						</Button>
					</DialogFooter>
				</Stack>
			)}
		</FormContext>
	);
};
