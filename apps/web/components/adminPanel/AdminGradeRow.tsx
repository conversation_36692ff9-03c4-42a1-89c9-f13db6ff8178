import { useSortable } from '@dnd-kit/sortable';
import { CSS } from '@dnd-kit/utilities';
import { PriceRoundingStrategyMessage } from '@pocitarna-nx-2023/config';
import { DeleteDialog, Icon, Stack, TableCell, TableRow, toast } from '@pocitarna-nx-2023/ui';
import { formatPercentage } from '@pocitarna-nx-2023/utils';
import { type ApiBody } from '@pocitarna-nx-2023/zodios';
import { apiHooks } from '@pocitarna-nx-2023/zodios-client';
import Link from 'next/link';
import { type FC } from 'react';

type Props = {
	grade: ApiBody<'getGrades'>[number];
};

export const AdminGradeRow: FC<Props> = ({ grade }) => {
	const { invalidate } = apiHooks.useGetRankedGrades({}, { enabled: false });
	const { mutate, isLoading } = apiHooks.useDeleteGrade({ params: { gradeId: grade.id } });
	const { attributes, listeners, setNodeRef, setActivatorNodeRef, transform, transition } = useSortable({ id: grade.id });

	const onGradeDelete = (callback: () => void) =>
		mutate(undefined, {
			onSuccess: () => {
				callback();
				toast.success('Stav bylo smazáno.');
				invalidate();
			},
		});

	return (
		<TableRow ref={setNodeRef} style={{ transform: CSS.Transform.toString(transform), transition }} {...attributes} role={undefined}>
			<TableCell className="cursor-grab" ref={setActivatorNodeRef} {...listeners}>
				<Icon name="grip-dots" />
			</TableCell>
			<TableCell>
				<Link className="text-link" href={`/admin/grade/${grade.id}`}>
					{grade.name}
				</Link>
			</TableCell>
			<TableCell>{grade.maxCosmeticDefects}</TableCell>
			<TableCell>{formatPercentage(grade.discountPercentage)}</TableCell>
			<TableCell>{PriceRoundingStrategyMessage[grade.priceRoundingStrategy]}</TableCell>

			<TableCell>
				<Stack direction="row" gap={2} className="justify-end">
					<DeleteDialog
						title="Odstranit stav"
						description="Opravdu si přejete odstranit položku?"
						triggerVariant="icon"
						onDelete={onGradeDelete}
						isLoading={isLoading}
					/>
				</Stack>
			</TableCell>
		</TableRow>
	);
};
