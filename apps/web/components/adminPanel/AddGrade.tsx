import { PRICE_ROUNDING_STRATEGIES, PriceRoundingStrategyMessage } from '@pocitarna-nx-2023/config';
import {
	Button,
	ComboboxControl,
	DialogFooter,
	FormContext,
	InputControl,
	NumericInputControl,
	Stack,
	useDialog,
} from '@pocitarna-nx-2023/ui';
import { grade } from '@pocitarna-nx-2023/zodios';
import { apiHooks } from '@pocitarna-nx-2023/zodios-client';
import { type FC } from 'react';

type Props = {
	gradesCount: number;
};

export const AddGrade: FC<Props> = ({ gradesCount }) => {
	const { closeDialog } = useDialog();
	const { invalidate } = apiHooks.useGetRankedGrades({}, { enabled: false });
	const { mutate, isLoading } = apiHooks.useCreateGrade(undefined);

	return (
		<FormContext
			schema={grade.omit({ id: true })}
			defaultValues={{
				name: '',
				maxCosmeticDefects: 0,
				discountPercentage: 0,
				priceRoundingStrategy: 'LOW',
				sequence: gradesCount,
			}}
			onSubmit={(data) => {
				mutate(data, {
					onSuccess: () => {
						invalidate();
						closeDialog();
					},
				});
			}}
		>
			{(control) => (
				<Stack gap={4}>
					<InputControl control={control} name="name" placeholder="Name" label="Název stavu" />
					<NumericInputControl
						control={control}
						name="maxCosmeticDefects"
						placeholder="0"
						label="Maximální počet kosmetických vad"
						numberFormat="integer"
					/>
					<NumericInputControl
						control={control}
						name="discountPercentage"
						label="Sleva z doporučené ceny (%)"
						numberFormat="decimal"
					/>
					<ComboboxControl
						control={control}
						name="priceRoundingStrategy"
						items={PRICE_ROUNDING_STRATEGIES.map((strategy) => ({
							key: strategy,
							value: strategy,
							label: PriceRoundingStrategyMessage[strategy],
						}))}
						label="Způsob zaokrouhlování ceny"
						hideSearch={true}
					/>

					<DialogFooter>
						<Button className="w-full" variant="default" type="submit" isLoading={isLoading}>
							Vytvořit
						</Button>
					</DialogFooter>
				</Stack>
			)}
		</FormContext>
	);
};
