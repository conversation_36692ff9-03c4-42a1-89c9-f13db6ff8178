import { PRICE_ROUNDING_STRATEGIES, PriceRoundingStrategyMessage } from '@pocitarna-nx-2023/config';
import { Button, ComboboxControl, FormContext, InputControl, NumericInputControl, Stack, toast } from '@pocitarna-nx-2023/ui';
import { type ApiBody, gradeUpdate } from '@pocitarna-nx-2023/zodios';
import { apiHooks } from '@pocitarna-nx-2023/zodios-client';
import { type FC } from 'react';

type Props = {
	grade: ApiBody<'getGrade'>;
};

export const EditGrade: FC<Props> = ({ grade }) => {
	const { invalidate } = apiHooks.useGetRankedGrades({}, { enabled: false });
	const { invalidate: invalidateGrade } = apiHooks.useGetGrade({ params: { gradeId: grade.id } }, { enabled: false });

	const { mutate, isLoading } = apiHooks.useUpdateGrade({ params: { gradeId: grade.id } });

	return (
		<FormContext
			schema={gradeUpdate}
			defaultValues={{
				name: grade.name ?? '',
				maxCosmeticDefects: grade.maxCosmeticDefects ?? 0,
				discountPercentage: grade.discountPercentage ? grade.discountPercentage * 100 : 0,
				priceRoundingStrategy: grade.priceRoundingStrategy ?? 'DOWN',
			}}
			onSubmit={(data) => {
				mutate(data, {
					onSuccess: () => {
						invalidate();
						invalidateGrade();
						toast.success('Zaktualizováno');
					},
				});
			}}
		>
			{(control) => (
				<div className="grid grid-cols-1 md:grid-cols-2 gap-4">
					<div className="col-span-2">
						<InputControl control={control} name="name" placeholder="Název stavu" label="Název stavu" />
					</div>
					<NumericInputControl
						control={control}
						name="maxCosmeticDefects"
						placeholder="0"
						label="Maximální počet kosmetických vad"
						numberFormat="integer"
					/>
					<NumericInputControl
						control={control}
						name="discountPercentage"
						placeholder="0"
						label="Sleva z doporučené ceny (%)"
						numberFormat="decimal"
					/>
					<ComboboxControl
						control={control}
						name="priceRoundingStrategy"
						items={PRICE_ROUNDING_STRATEGIES.map((strategy) => ({
							key: strategy,
							value: strategy,
							label: PriceRoundingStrategyMessage[strategy],
						}))}
						label="Způsob zaokrouhlování ceny"
						hideSearch={true}
					/>
					<Stack gap={4} className="col-span-2">
						<Button type="submit" isLoading={isLoading}>
							Uložit
						</Button>
					</Stack>
				</div>
			)}
		</FormContext>
	);
};
