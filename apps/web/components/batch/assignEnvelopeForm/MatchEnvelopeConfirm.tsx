import { Button, Typography } from '@pocitarna-nx-2023/ui';
import { type ApiBody } from '@pocitarna-nx-2023/zodios';
import { apiHooks } from '@pocitarna-nx-2023/zodios-client';
import { type FC } from 'react';
import { EnvelopeRecap } from '../../productEnvelope/EnvelopeRecap';

type Props = {
	envelopeMatch: ApiBody<'getProductEnvelope'>;
	onSuccess: () => void;
	productIds: string[];
};

export const MatchEnvelopeConfirm: FC<Props> = ({ envelopeMatch, productIds, onSuccess }) => {
	const { mutate: assignProductEnvelope, isLoading } = apiHooks.useAssignProductEnvelope({ params: { productId: productIds[0] } });
	const { mutate: bulkAssignProductEnvelope, isLoading: isBulkLoading } = apiHooks.useBulkAssignProductEnvelope();

	const onSelect = () => {
		if (productIds.length > 1) {
			bulkAssignProductEnvelope({ ids: productIds, data: { productEnvelopeId: envelopeMatch.id } }, { onSuccess });
		} else {
			assignProductEnvelope({ productEnvelopeId: envelopeMatch.id }, { onSuccess });
		}
	};

	return (
		<div className="p-4 bg-yellow-100 border border-yellow-200 rounded-md">
			<Typography>
				Na základě vybraných parametrů byla nalezena existující karta produktu. Vyberte prosím tuto existující kartu:
			</Typography>
			<br />
			<EnvelopeRecap productEnvelope={envelopeMatch} />
			<br />
			<Button isLoading={isLoading || isBulkLoading} type="button" width="full" onClick={onSelect}>
				Přiřadit kartu
			</Button>
		</div>
	);
};
