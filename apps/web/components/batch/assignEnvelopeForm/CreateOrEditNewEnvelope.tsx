import { MAX_POSITIVE_INTEGER } from '@pocitarna-nx-2023/config';
import {
	Button,
	ComboboxControl,
	DialogFooter,
	FormContext,
	type FormContextRef,
	InputControl,
	NumericInputControl,
	Stack,
	toast,
} from '@pocitarna-nx-2023/ui';
import { type SingleOrArray, uniques } from '@pocitarna-nx-2023/utils';
import { type ApiBody, primitive, productEnvelopeCreate } from '@pocitarna-nx-2023/zodios';
import { apiHooks } from '@pocitarna-nx-2023/zodios-client';
import { type FC, useEffect, useMemo, useRef, useState } from 'react';
import { z } from 'zod';
import { useCategoryAttributes } from '../../../hooks/useCategoryAttributes';
import { useEnvelopeMappedAttributeValues } from '../../../hooks/useEnvelopeMappedAttributeValues';
import { useSearchFilter } from '../../../hooks/useSearchFilter';
import { MatchEnvelopeConfirm } from './MatchEnvelopeConfirm';
import { NewEnvelopeAttributeValues } from './NewEnvelopeAttributeValues';

// FIXME: To remove reference to Stav attr value in AMOUNT envelope branch!
type Props = {
	productId: SingleOrArray<string>;
	onSuccess: () => void;
	productCategory?: string;
	productEnvelope?: ApiBody<'getBatchProducts'>[number]['productEnvelope'];
	variant: 'create' | 'edit';
};

const rawAttributeValue = z.object({
	attributeValueId: z.string().optional(),
	value: primitive.optional(),
});

export type RawAttributeValue = z.infer<typeof rawAttributeValue>;

const schema = productEnvelopeCreate.omit({ attributeValues: true }).extend({
	attributeValues: z.record(z.string(), rawAttributeValue).optional(),
});

export const CreateOrEditNewEnvelope: FC<Props> = ({ productId, productEnvelope, productCategory, onSuccess, variant }) => {
	const [categoryId, setCategoryId] = useState<string | null>(productCategory ?? null);
	const [envelopeMatch, setEnvelopeMatch] = useState<ApiBody<'getProductEnvelope'> | null>(null);

	const { updateSearchTerm, filter } = useSearchFilter('name');
	const formRef = useRef<FormContextRef<typeof schema>>(null);

	const isEdit = variant === 'edit' && productEnvelope != null;
	const isBulkUpdate = useMemo(() => Array.isArray(productId), [productId]);
	const productIds = useMemo(() => (isBulkUpdate ? (productId as string[]) : ([productId] as string[])), [isBulkUpdate, productId]);
	const idsToProcess = uniques(productIds);

	const { data: productCategoriesData } = apiHooks.useGetProductCategoriesList({
		queries: { page: 1, limit: MAX_POSITIVE_INTEGER, sort: ['name'], filter },
	});
	const productCategories = useMemo(() => productCategoriesData?._data ?? [], [productCategoriesData?._data]);

	const { mutate: createNewEnvelope, isLoading } = apiHooks.useCreateProductEnvelope();

	const { mutate: updateProductEnvelope, isLoading: isUpdating } = apiHooks.useUpdateProductEnvelope({
		params: { productEnvelopeId: productEnvelope?.id ?? '' },
	});

	const { categoryAttributes } = useCategoryAttributes(categoryId, 'envelope');

	const { invalidate: invalidateEnvelopes } = apiHooks.useGetAllProductEnvelopes({}, { enabled: false });

	const attributesToShow = useMemo(() => {
		return categoryAttributes.filter((attribute) => !['Stav', 'Typ produktu', 'Stav - poznámka'].includes(attribute.displayName));
	}, [categoryAttributes]);

	const mappedAttributesValue = useEnvelopeMappedAttributeValues(attributesToShow, productEnvelope?.id);

	const productTypeAttribute = useMemo(() => {
		return categoryAttributes.find((attribute) => attribute.displayName === 'Typ produktu');
	}, [categoryAttributes]);

	const conditionAttributeValue = useMemo(() => {
		return categoryAttributes.find((attribute) => attribute.displayName === 'Stav');
	}, [categoryAttributes]);

	const { data: newProductTypeAttributeValueData } = apiHooks.useGetRawAttributeValues(
		{
			queries: {
				page: 1,
				limit: 1,
				filter: { attributeId: { eq: productTypeAttribute?.id ?? '' }, 'value->>0': { eq: 'Nové' } },
			},
		},
		{ enabled: Boolean(productTypeAttribute?.id) },
	);

	const { data: conditionAAttributeValueData } = apiHooks.useGetRawAttributeValues(
		{
			queries: {
				page: 1,
				limit: 1,
				filter: { attributeId: { eq: conditionAttributeValue?.id ?? '' }, 'value->>0': { eq: 'A' } },
			},
		},
		{ enabled: Boolean(conditionAttributeValue?.id) },
	);

	const newProductTypeAttributeValue = newProductTypeAttributeValueData?._data?.[0];
	const conditionAAttributeValue = conditionAAttributeValueData?._data?.[0];

	const defaultAttributeValues = useMemo(() => {
		return [
			...(newProductTypeAttributeValue
				? [
						{
							attributeId: newProductTypeAttributeValue.attributeId,
							attributeValueId: newProductTypeAttributeValue.id,
						},
					]
				: []),
			...(conditionAAttributeValue
				? [
						{
							attributeId: conditionAAttributeValue.attributeId,
							attributeValueId: conditionAAttributeValue.id,
						},
					]
				: []),
		];
	}, [newProductTypeAttributeValue, conditionAAttributeValue]);

	const defaultValues = useMemo(
		() => ({
			productCategory: categoryId ? { id: categoryId } : null,
			name: productEnvelope?.name ?? '',
			ean: productEnvelope?.ean ?? '',
			vendorCode: productEnvelope?.vendorCode ?? '',
			salePrice: productEnvelope?.salePrice ?? 0,
			standardPrice: productEnvelope?.standardPrice ?? 0,
			type: productEnvelope?.type ?? ('NEW' as const),
			products: idsToProcess.map((id) => ({ id })),
			needsRefinement: true,
			attributeValues: attributesToShow.reduce<Record<string, { value?: string; attributeValueId?: string }>>((acc, attribute) => {
				const match = isEdit ? mappedAttributesValue.find((item) => item.attributeId === attribute.id)?.matches[0] : null;
				acc[attribute.id] = match ? { attributeValueId: match.id } : { value: '', attributeValueId: '' };
				return acc;
			}, {}),
		}),
		[categoryId, productEnvelope, idsToProcess, attributesToShow, mappedAttributesValue, isEdit],
	);

	const onMatchFound = (envelope: ApiBody<'getProductEnvelope'> | null) => {
		if (envelope) toast.warning('Nalezena existující karta produktu');
		setEnvelopeMatch(envelope);
	};

	const onDone = () => {
		onSuccess();
		invalidateEnvelopes();
	};

	useEffect(() => {
		if (isEdit) formRef?.current?.reset(defaultValues);
	}, [defaultValues, isEdit]);

	return (
		<FormContext
			ref={formRef}
			schema={schema}
			defaultValues={defaultValues}
			onSubmit={(data) => {
				if (isEdit) {
					updateProductEnvelope(
						{
							...data,
							attributeValues: [...defaultAttributeValues, ...transformAttributeValues(data.attributeValues ?? {})],
						},
						{
							onSuccess: onDone,
						},
					);
				} else {
					createNewEnvelope(
						{
							...data,
							attributeValues: [...defaultAttributeValues, ...transformAttributeValues(data.attributeValues ?? {})],
						},
						{
							onSuccess: onDone,
						},
					);
				}
			}}
		>
			{(control) => (
				<Stack gap={4}>
					<ComboboxControl
						label="Kategorie produktu"
						control={control}
						name="productCategory.id"
						onSearchChange={updateSearchTerm}
						items={productCategories.map((category) => ({ label: category.name, value: category.id }))}
						onSelect={(value) => setCategoryId(value)}
					/>
					<InputControl control={control} name="name" label="Název karty" />
					<InputControl control={control} name="ean" label="EAN" />
					<InputControl control={control} name="vendorCode" label="Dodavatelský kód" />
					<NumericInputControl
						control={control}
						numberFormat="decimal"
						name="standardPrice"
						placeholder="Standardní cena"
						label="Standardní cena"
					/>
					<NumericInputControl
						control={control}
						numberFormat="decimal"
						name="salePrice"
						placeholder="Prodejní cena"
						label="Prodejní cena"
					/>

					{categoryId && attributesToShow.length > 0 && (
						<NewEnvelopeAttributeValues
							attributesToShow={attributesToShow}
							categoryId={categoryId}
							defaultAttributeValues={defaultAttributeValues}
							onMatchFound={onMatchFound}
							productEnvelope={productEnvelope}
						/>
					)}

					{envelopeMatch && (
						<MatchEnvelopeConfirm envelopeMatch={envelopeMatch} productIds={idsToProcess} onSuccess={onSuccess} />
					)}

					<DialogFooter>
						<Button type="submit" isLoading={isLoading || isUpdating} disabled={envelopeMatch != null}>
							Uložit
						</Button>
					</DialogFooter>
				</Stack>
			)}
		</FormContext>
	);
};

export const transformAttributeValues = (attributeValues: Record<string, RawAttributeValue>) => {
	return Object.entries(attributeValues)
		.map(([key, { value, attributeValueId }]) => ({
			attributeId: key,
			value: value !== '' ? value : undefined,
			attributeValueId: attributeValueId !== '' ? attributeValueId : undefined,
		}))
		.filter(({ attributeValueId, value }) => attributeValueId !== undefined || value !== undefined);
};
