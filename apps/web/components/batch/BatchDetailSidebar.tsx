import { Stack } from '@pocitarna-nx-2023/ui';
import { type ApiBody } from '@pocitarna-nx-2023/zodios';
import { type FC } from 'react';
import { useBatchDetailPageData } from '../../contexts/BatchDetailPageDataProvider';
import { BatchDetailOverview } from './BatchDetailOverview';
import { ClosedBatchAnalytics } from './ClosedBatchAnalytics';

type Props = {
	batch: ApiBody<'getBatch'>;
};

export const BatchDetailSidebar: FC<Props> = ({ batch }) => {
	const { snDuplicatesMessage } = useBatchDetailPageData();

	return (
		<Stack>
			{snDuplicatesMessage}
			<BatchDetailOverview batch={batch} />
			{batch.status === 'CLOSED' && <ClosedBatchAnalytics batchId={batch.id} />}
		</Stack>
	);
};
