import { PRODUCT_TYPES, ProductTypeMessage } from '@pocitarna-nx-2023/config';
import { Button, ComboboxControl, FormContext, Label, Stack, TableCell, TableRow, toast } from '@pocitarna-nx-2023/ui';
import { type ApiBody } from '@pocitarna-nx-2023/zodios';
import { apiHooks } from '@pocitarna-nx-2023/zodios-client';
import { type FC } from 'react';
import { z } from 'zod';

type Props = {
	product: ApiBody<'getProduct'>;
};

export const TestDiffInputType: FC<Props> = ({ product }) => {
	const { invalidate: invalidateBatchProducts } = apiHooks.useGetBatchProducts(
		{ params: { batchId: product.batchId ?? '' } },
		{ enabled: false },
	);

	const { invalidate: invalidateProduct } = apiHooks.useGetProduct({ params: { productId: product.id } }, { enabled: false });
	const { mutateAsync: updateProduct, isLoading } = apiHooks.useUpdateProduct({ params: { productId: product.id } });

	return (
		<TableRow>
			<TableCell>
				<Label htmlFor="type">Typ</Label>
			</TableCell>
			<TableCell>
				<FormContext
					schema={z.object({
						type: z.enum(PRODUCT_TYPES),
					})}
					defaultValues={{
						type: product.type ?? 'REFURBISHED',
					}}
					onSubmit={({ type }) => {
						updateProduct(
							{
								type,
							},
							{
								onSuccess: () => {
									invalidateProduct();
									invalidateBatchProducts();
									toast.success('Aktualizováno');
								},
							},
						);
					}}
				>
					{(control) => (
						<Stack direction="row" gap={4}>
							<div className="w-full">
								<ComboboxControl
									control={control}
									name="type"
									items={PRODUCT_TYPES.map((type) => ({ value: type, label: ProductTypeMessage[type] }))}
								/>
							</div>
							<Button type="submit" isLoading={isLoading}>
								Uložit
							</Button>
						</Stack>
					)}
				</FormContext>
			</TableCell>
		</TableRow>
	);
};
