import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@pocitarna-nx-2023/ui';
import { apiHooks } from '@pocitarna-nx-2023/zodios-client';
import { type FC, useMemo } from 'react';
import { type DateRange } from 'react-day-picker';
import { ErrorRateView } from '../ErrorRateView';

type Props = {
	range: DateRange | undefined;
	tester: string;
};

export const FixedAttributesBreakdownView: FC<Props> = ({ range, tester }) => {
	const { data } = apiHooks.useGetTestingFixesAnalytics(
		{
			queries: {
				startDate: range?.from ?? new Date(),
				endDate: range?.to ?? new Date(),
				...(tester !== 'ALL' ? { testerId: tester } : {}),
			},
		},
		{
			enabled: range?.from != null && range?.to != null,
		},
	);

	const fixedAttributesData = useMemo(() => data?._data, [data?._data]);
	const sortedFixedAttributes = useMemo(
		() => (fixedAttributesData?.fixedAttributes ?? []).sort((a, b) => b[1] - a[1]),
		[fixedAttributesData?.fixedAttributes],
	);

	if (!fixedAttributesData) return null;

	return (
		<Table>
			<TableHeader>
				<TableRow>
					<TableHead>Název</TableHead>
					<TableHead>Počet</TableHead>
					<TableHead>Chybovost</TableHead>
				</TableRow>
			</TableHeader>
			<TableBody>
				{sortedFixedAttributes.map(([name, amount]) => (
					<TableRow key={name}>
						<>
							<TableCell>{name}</TableCell>
							<TableCell>{amount}</TableCell>
							<TableCell>
								<ErrorRateView total={fixedAttributesData.total} errors={amount} />
							</TableCell>
						</>
					</TableRow>
				))}
			</TableBody>
		</Table>
	);
};
