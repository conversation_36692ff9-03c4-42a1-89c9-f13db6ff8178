import { NOT_AVAILABLE } from '@pocitarna-nx-2023/config';
import { Card, type FilterRules, Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@pocitarna-nx-2023/ui';
import { formatPercentage } from '@pocitarna-nx-2023/utils';
import { type ApiBody } from '@pocitarna-nx-2023/zodios';
import Link from 'next/link';
import { type FC, memo, useMemo } from 'react';
import { useSortInPlace } from '../../../hooks/useSortInPlace';
import { PriceDisplayer } from '../../PriceDisplayer';
import { SortableColumn } from '../../sorting/SortableColumn';
import { AttributeValuesOverview } from '../../stock/AttributeValuesOverview';

type Props = {
	title: string;
	categoryData: ApiBody<'getAgingStockAnalytics'>[number]['data'];
	filters: FilterRules;
};
const ListTable: FC<Props> = ({ title, categoryData, filters }) => {
	const filteredRows = useMemo(
		() =>
			categoryData.filter((entry: ApiBody<'getAgingStockAnalytics'>[number]['data'][number]) => {
				if (
					filters['manufacturer'] &&
					!entry.attributesOverview.brandStockAttributeValues.some((attr) =>
						attr.matches.some((match) =>
							(match.value as string).toLowerCase().includes((filters['manufacturer'] as string).toLowerCase()),
						),
					)
				) {
					return false;
				}

				return true;
			}),
		[categoryData, filters],
	);

	const { sortedRows } = useSortInPlace(filteredRows);

	return (
		<Card>
			<Table expandable title={title}>
				<TableHeader>
					<TableRow>
						<TableHead>Kód produktu</TableHead>
						<TableHead>Produkt</TableHead>
						<SortableColumn property={'stockAmount'}>Počet kusů skladem</SortableColumn>
						<SortableColumn property="envelopeSalePrice">Prodejní cena</SortableColumn>
						<TableHead>Nákupní cena</TableHead>
						<SortableColumn property="daysWithoutMovement">Dny bez pohybu</SortableColumn>
						<SortableColumn property="tempo">Tempo</SortableColumn>
					</TableRow>
				</TableHeader>
				<TableBody>
					{sortedRows.map((entry) => (
						<StockRow key={entry.envelopeId} item={entry} />
					))}
				</TableBody>
			</Table>
		</Card>
	);
};

export const StockTable = memo(ListTable);
StockTable.displayName = 'StockTable';

const StockRow: FC<{ item: ApiBody<'getAgingStockAnalytics'>[number]['data'][number] }> = ({ item }) => {
	const { envelopeCode, envelopeId, stockAmount, attributesOverview, averageBuyPrice, envelopeSalePrice, daysWithoutMovement, tempo } =
		item;

	return (
		<TableRow>
			<TableCell>
				<Link className="text-link" href={`/product/envelope/${envelopeId}`}>
					{envelopeCode}
				</Link>
			</TableCell>
			<TableCell>
				<AttributeValuesOverview
					brandStockAttributeValues={attributesOverview.brandStockAttributeValues}
					modelStockAttributeValues={attributesOverview.modelStockAttributeValues}
					otherStockAttributeValues={attributesOverview.otherStockAttributeValues}
				/>
			</TableCell>
			<TableCell>{stockAmount}</TableCell>
			<TableCell>
				<PriceDisplayer price={envelopeSalePrice} />
			</TableCell>
			<TableCell>
				<PriceDisplayer price={averageBuyPrice} />
			</TableCell>
			<TableCell>{daysWithoutMovement ?? NOT_AVAILABLE}</TableCell>
			<TableCell>{tempo != null ? formatPercentage(tempo) : NOT_AVAILABLE}</TableCell>
		</TableRow>
	);
};
