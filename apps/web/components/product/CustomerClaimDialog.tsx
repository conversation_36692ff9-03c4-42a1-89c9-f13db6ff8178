import { But<PERSON>, <PERSON><PERSON>, <PERSON>alog<PERSON>ontent, <PERSON>alogHeader, DialogTitle, DialogTrigger, Icon, useDialog } from '@pocitarna-nx-2023/ui';
import type { ApiBody } from '@pocitarna-nx-2023/zodios';
import { apiHooks } from '@pocitarna-nx-2023/zodios-client';
import { useRouter } from 'next/router';
import { type FC } from 'react';
import { AddProductDefect } from '../productDefect/AddProductDefect';

type Props = {
	product: ApiBody<'getProduct'>;
};

export const CustomerClaimDialog: FC<Props> = ({ product }) => {
	const trigger = (
		<Button variant="secondary">
			<Icon name="bullhorn" />
			Nová zákaznická reklamace
		</Button>
	);

	return (
		<Dialog>
			<DialogTrigger asChild>{trigger}</DialogTrigger>
			<DialogContent size="lg">
				<DialogHeader>
					<DialogTitle>Nov<PERSON> z<PERSON>aznická reklamace</DialogTitle>
				</DialogHeader>
				<CustomerClaimForm product={product} />
			</DialogContent>
		</Dialog>
	);
};

export const CustomerClaimForm: FC<Props> = ({ product }) => {
	const router = useRouter();
	const { closeDialog } = useDialog();
	const { invalidate: invalidateProductDefects } = apiHooks.useGetProductDefects(
		{
			params: { productId: product.id },
		},
		{ enabled: false },
	);
	const { invalidate: invalidateCustomerClaims } = apiHooks.useGetCustomerClaims({}, { enabled: false });
	const { invalidate: invalidateProducts } = apiHooks.useGetAllProducts({}, { enabled: false });
	const { invalidate: invalidateProduct } = apiHooks.useGetProduct({ params: { productId: product.id } }, { enabled: false });
	const { mutate: createCustomerClaim, isLoading } = apiHooks.useCreateCustomerClaim(undefined, {
		onSuccess: (data) => {
			closeDialog();
			invalidateProducts();
			invalidateProduct();
			invalidateCustomerClaims();
			invalidateProductDefects();
			router.push(`/customer-claim/${data._data.id}`);
		},
	});

	const onDefectCreation = async (productDefect: ApiBody<'createProductDefect'>) => {
		createCustomerClaim({ note: productDefect.note ?? '', productDefects: [productDefect], productId: product.id });
	};

	return <AddProductDefect product={product} onSuccess={onDefectCreation} source="CUSTOMER_CLAIM" isStillLoading={isLoading} />;
};
