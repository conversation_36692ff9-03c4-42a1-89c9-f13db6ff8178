import { MAX_POSITIVE_INTEGER, type ProductFileType, productFileTypeMap, TEN_SECONDS } from '@pocitarna-nx-2023/config';
import { Media, ParamItem, ParamList, ParamSpacer } from '@pocitarna-nx-2023/ui';
import { formatProductCode } from '@pocitarna-nx-2023/utils';
import { type ApiBody } from '@pocitarna-nx-2023/zodios';
import { apiHooks } from '@pocitarna-nx-2023/zodios-client';
import { type FC, Fragment, type ReactNode, useMemo } from 'react';
import { useUserHasScope } from '../../hooks/useUserHasScope';
import { FileUploader } from '../FileUploader';

type Props = {
	product: ApiBody<'getProduct'>;
	description?: ReactNode;
	disabled?: boolean;
};

export const ProductFilesCard: FC<Props> = ({ product, description, disabled = false }) => {
	const isAdmin = useUserHasScope('admin');
	const isTestLead = useUserHasScope('productTestLead');

	const { data: productFilesData } = apiHooks.useGetProductFiles(
		{
			params: { productId: product.id },
			queries: {
				page: 1,
				limit: MAX_POSITIVE_INTEGER,
			},
		},
		{ refetchInterval: TEN_SECONDS },
	);

	const productFileGroups = useMemo(
		() =>
			(productFilesData?._data ?? []).reduce(
				(
					acc: { type: ProductFileType; files: ApiBody<'getProductFiles'>[number]['file'][] }[],
					{ file, type }: ApiBody<'getProductFiles'>[number],
				) => {
					const group = acc.find((group) => group.type === type);
					if (group) {
						group.files.push(file);
					} else {
						acc.push({ type, files: [file] });
					}
					return acc;
				},
				[],
			),
		[productFilesData?._data],
	);
	const { invalidate: invalidateProductFiles } = apiHooks.useGetProductFiles(
		{ params: { productId: product.id }, queries: { page: 1, limit: MAX_POSITIVE_INTEGER } },
		{ enabled: false },
	);
	const { mutate: deleteFiles, isLoading: isDeletingFiles } = apiHooks.useDeleteFiles(undefined, {
		onSuccess: () => invalidateProductFiles(),
	});
	const { mutate: rotateImageFile, isLoading: isRotatingFile } = apiHooks.useRotateImageFile(undefined, {
		onSuccess: () => invalidateProductFiles(),
	});
	const { mutate: updateSequence } = apiHooks.useUpdateProductFileSequence(
		{ params: { productId: product.id } },
		{ onSuccess: () => invalidateProductFiles() },
	);
	const { mutateAsync: addFilesToProduct, isLoading: isAddingFiles } = apiHooks.useAddFilesToProduct(
		{ params: { productId: product.id } },
		{ onSuccess: () => invalidateProductFiles() },
	);

	return (
		<FileUploader
			pageType="detail"
			inputName="files"
			entityCode={formatProductCode(product.code)}
			entityId={product.id}
			entityType="product"
			isAddingFiles={isAddingFiles}
			addFilesToEntity={addFilesToProduct}
			description={description}
			disabled={disabled}
		>
			<ParamList lineBreak={true} className="empty:hidden">
				{productFileGroups.map(({ type, files }, index) => (
					<Fragment key={type}>
						{index > 0 && <ParamSpacer lineBreak={true} />}
						<ParamItem label={productFileTypeMap[type]}>
							<Media
								files={files}
								moveTo={!disabled ? (file, sequence) => updateSequence({ fileId: file.id, sequence }) : undefined}
								onDelete={
									disabled || (type !== 'regular' && !isAdmin && !isTestLead)
										? undefined
										: (file) => deleteFiles({ ids: [file.id] })
								}
								onRotation={disabled ? undefined : (file, rotation) => rotateImageFile({ fileId: file.id, rotation })}
								isLoading={isAddingFiles || isDeletingFiles || isRotatingFile}
							/>
						</ParamItem>
					</Fragment>
				))}
			</ParamList>
		</FileUploader>
	);
};
