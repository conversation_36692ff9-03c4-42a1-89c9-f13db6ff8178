import { CustomerClaimStatusMessage } from '@pocitarna-nx-2023/config';
import { Button, toast } from '@pocitarna-nx-2023/ui';
import { type ApiBody } from '@pocitarna-nx-2023/zodios';
import { apiHooks } from '@pocitarna-nx-2023/zodios-client';
import { type FC } from 'react';
import { ProductDefectSolverDialog } from '../productDefect/ProductDefectSolverDialog';

type Props = {
	productId: string;
	productDefects: ApiBody<'getAllProductDefects'>;
	customerClaim: ApiBody<'getCustomerClaim'>;
	disabled: boolean;
};

export const CustomerClaimFlowHandler: FC<Props> = ({ productId, productDefects, disabled, customerClaim }) => {
	const unsolvedDefects = productDefects.filter((defect) => defect.serviceTask?.status !== 'CLOSED');

	const { mutate: updateCustomerClaim, isLoading: isUpdatingCustomerClaim } = apiHooks.useUpdateCustomerClaim({
		params: { customerClaimId: customerClaim.id },
	});

	const { invalidate: invalidateProductDefects } = apiHooks.useGetAllProductDefects(undefined, { enabled: false });
	const { invalidate: invalidateCustomerClaimHistory } = apiHooks.useGetCustomerClaimHistory(
		{ params: { customerClaimId: customerClaim.id } },
		{ enabled: false },
	);
	const { invalidate: invalidateCustomerClaim } = apiHooks.useGetCustomerClaim(
		{ params: { customerClaimId: customerClaim.id } },
		{ enabled: false },
	);

	if (unsolvedDefects.length === 0) {
		return (
			<Button
				disabled={disabled}
				onClick={() => {
					if (disabled) return;
					updateCustomerClaim(
						{
							status: 'CLOSED',
							productId,
						},
						{
							onSuccess: () => {
								toast.success(`Upraveno do stavu ${CustomerClaimStatusMessage['CLOSED']}`);
								invalidateCustomerClaimHistory();
								invalidateProductDefects();
								invalidateCustomerClaim();
							},
						},
					);
				}}
				isLoading={isUpdatingCustomerClaim}
			>
				Uzavřít reklamaci
			</Button>
		);
	}

	return <ProductDefectSolverDialog unsolvedDefects={unsolvedDefects} disabled={disabled} source="CUSTOMER_CLAIM" />;
};
