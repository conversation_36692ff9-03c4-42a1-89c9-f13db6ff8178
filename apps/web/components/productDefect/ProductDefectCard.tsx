import { MAX_POSITIVE_INTEGER, NOT_AVAILABLE, type ProductDefectSource } from '@pocitarna-nx-2023/config';
import { CardContent, CardDescription, CardHeader, CardTitle, cn, DeleteDialog, Media, Stack, useDialog } from '@pocitarna-nx-2023/ui';
import { type ApiBody } from '@pocitarna-nx-2023/zodios';
import { apiHooks } from '@pocitarna-nx-2023/zodios-client';
import { type FC } from 'react';
import { useUserHasScope } from '../../hooks/useUserHasScope';
import { MarkdownRenderer } from '../MarkdownRenderer';
import { DefectCardWrapper } from './DefectCardWrapper';
import { ProductDefectResolution } from './ProductDefectResolution';
import { ServiceTaskEditDialog } from './ServiceTaskEditDialog';
import { UncoveredDefectWarning } from './UncoveredDefectWarning';

type Props = {
	defect: ApiBody<'getProductDefects'>[number];
	product: ApiBody<'getProduct'>;
	source?: ProductDefectSource;
	isUncovered?: boolean;
};

export const ProductDefectCard: FC<Props> = ({ defect, isUncovered, source, product }) => {
	const isAdmin = useUserHasScope('admin');
	const isProductWrite = useUserHasScope('productWrite');
	const isServiceManager = useUserHasScope('serviceManage');
	const isProductTest = useUserHasScope('productTest');
	const serviceTask = defect.serviceTask;
	const vendorTask = defect.vendorTask;
	const { closeDialog } = useDialog();

	const { data: defectFilesData, invalidate: invalidateProductDefectFiles } = apiHooks.useGetProductDefectFiles({
		params: { defectId: defect.id },
		queries: { page: 1, limit: MAX_POSITIVE_INTEGER },
	});
	const { mutate: deleteFiles, isLoading: isDeletingFiles } = apiHooks.useDeleteFiles(undefined, {
		onSuccess: () => invalidateProductDefectFiles(),
	});
	const { mutate: rotateImageFile, isLoading: isRotatingFile } = apiHooks.useRotateImageFile(undefined, {
		onSuccess: () => invalidateProductDefectFiles(),
	});
	const { mutate: updateSequence } = apiHooks.useUpdateProductDefectFileSequence(
		{ params: { productDefectId: defect.id } },
		{ onSuccess: () => invalidateProductDefectFiles() },
	);
	const defectFiles = defectFilesData?._data ?? [];
	const productId = defect.productId;

	const { invalidate: invalidateAllProductDefects } = apiHooks.useGetAllProductDefects(undefined, { enabled: false });

	const { invalidate: invalidateProductDefects } = apiHooks.useGetProductDefects(
		{
			params: { productId: productId ?? '' },
		},
		{ enabled: false },
	);

	const { mutate: deleteProductDefect, isLoading: deleteDefectLoading } = apiHooks.useDeleteProductDefect(
		{ params: { productId: defect.productId, defectId: defect.id } },
		{
			onSuccess: () => {
				invalidateAllProductDefects();
				invalidateProductDefects();
				closeDialog();
			},
		},
	);

	const taskToShow = serviceTask ?? vendorTask;

	return (
		<DefectCardWrapper isUncovered={isUncovered}>
			<CardHeader
				className="flex flex-col justify-between items-start md:flex-row"
				actions={
					<>
						{isUncovered && <UncoveredDefectWarning />}
						{(isAdmin || isServiceManager) && taskToShow != null && (
							<ServiceTaskEditDialog serviceTask={taskToShow} productId={defect.productId} source={source} />
						)}
						{isAdmin && (
							<DeleteDialog
								title="Smazat vadu"
								description="Opravdu si přejete odstranit položku?"
								triggerVariant="icon"
								disabled={product.productDefects.length <= 1}
								onDelete={() => deleteProductDefect(undefined)}
								isLoading={deleteDefectLoading}
							/>
						)}
					</>
				}
			>
				<CardTitle className={cn(isUncovered && 'text-warning')}>{defect.defectType?.name ?? NOT_AVAILABLE}</CardTitle>
			</CardHeader>
			<CardContent>
				<Stack gap={4}>
					<CardDescription>
						<MarkdownRenderer value={defect.note} />
					</CardDescription>
					<Media
						files={defectFiles.map(({ file }) => file)}
						moveTo={
							isProductWrite || isProductTest ? (file, sequence) => updateSequence({ fileId: file.id, sequence }) : undefined
						}
						onDelete={isProductWrite || isProductTest ? (file) => deleteFiles({ ids: [file.id] }) : undefined}
						onRotation={
							isProductWrite || isProductTest ? (file, rotation) => rotateImageFile({ fileId: file.id, rotation }) : undefined
						}
						isLoading={isDeletingFiles || isRotatingFile}
					/>
					<ProductDefectResolution productDefect={defect} />
				</Stack>
			</CardContent>
		</DefectCardWrapper>
	);
};
