import { PRODUCT_DEFECT_SOURCE, type ProductDefectSource, REQUIRED_FIELD } from '@pocitarna-nx-2023/config';
import { SERVICE_CASE_TYPE_NAMES, ServiceCaseTypeMessage } from '@pocitarna-nx-2023/config';
import { Button, ComboboxControl, FormContext, Stack, TextControl, toast } from '@pocitarna-nx-2023/ui';
import { formatProductCode } from '@pocitarna-nx-2023/utils';
import { type ApiBody } from '@pocitarna-nx-2023/zodios';
import { apiHooks } from '@pocitarna-nx-2023/zodios-client';
import { useRouter } from 'next/router';
import { type FC, useEffect, useRef } from 'react';
import { useToggle } from 'rooks';
import { z } from 'zod';
import { useLazyComboboxProps } from '../../hooks/useLazyComboboxProps';
import { useSearchFilter } from '../../hooks/useSearchFilter';
import { TemporaryFileUploader } from '../TemporaryFileUploader';
import { ProductDefectFormCard } from './ProductDefectFormCard';

type ServiceCaseData =
	| {
			note: string;
			shouldRedirect?: boolean;
	  }
	| {
			id: string;
	  };

type Props = {
	product: ApiBody<'getProduct'>;
	onSuccess?: (data: ApiBody<'createProductDefect'>) => void;
	source: ProductDefectSource;
	isStillLoading?: boolean;
	order?: number;
	serviceCaseData?: ServiceCaseData;
	warrantyClaimId?: string;
	disabled?: boolean;
};

export const AddProductDefect: FC<Props> = ({
	product,
	onSuccess,
	order,
	source,
	serviceCaseData,
	isStillLoading,
	warrantyClaimId,
	disabled = false,
}) => {
	const cardRef = useRef<HTMLDivElement>(null);
	const [isUploadingFile, toggleUploadingFile] = useToggle();
	const router = useRouter();

	const serviceCaseId = serviceCaseData && 'id' in serviceCaseData ? serviceCaseData.id : null;
	const shouldCreateServiceCase = Boolean(serviceCaseData && !serviceCaseId && 'note' in serviceCaseData);

	const { filter, updateSearchTerm } = useSearchFilter('name');

	const { mutate: createDefect, isLoading: isUploadingForm } = apiHooks.useCreateProductDefect({ params: { productId: product.id } });
	const { invalidate: invalidateDefects } = apiHooks.useGetProductDefects({ params: { productId: product.id } }, { enabled: false });
	const { invalidate: invalidateProduct } = apiHooks.useGetProduct({ params: { productId: product.id } }, { enabled: false });
	const { invalidate: invalidateAllProductDefects } = apiHooks.useGetAllProductDefects({}, { enabled: false });

	useEffect(() => {
		if (cardRef.current) cardRef.current.scrollIntoView({ block: 'nearest', behavior: 'smooth' });
	}, [cardRef]);

	const lazyComboboxProps = useLazyComboboxProps<ApiBody<'getDefectTypes'>[number]>('getDefectTypes', {
		queries: {
			filter: {
				...filter,
				name: {
					...(filter?.['name' as keyof typeof filter] ?? {}),
					ne: 'Neshodující se parametry',
				},
				'productCategory.id': { eq: product.productCategoryId },
			},
			sort: ['name'],
		},
		formatResult: (type) => ({ value: type.id, label: type.name }),
	});

	return (
		<ProductDefectFormCard order={order}>
			<FormContext
				schema={z.object({
					note: z.string(),
					files: z.array(z.string()),
					defectType: z.string().uuid({ message: REQUIRED_FIELD }),
					source: z.enum(PRODUCT_DEFECT_SOURCE),
					serviceCaseType: z.enum(SERVICE_CASE_TYPE_NAMES).optional(),
					serviceCaseNote: z.string().optional(),
				})}
				defaultValues={{
					note: '',
					files: [] as string[],
					defectType: '',
					source,
					...(shouldCreateServiceCase
						? { serviceCaseType: 'BACKOFFICE', serviceCaseNote: (serviceCaseData as { note: string }).note }
						: { serviceCaseType: undefined, serviceCaseNote: undefined }),
				}}
				onSubmit={(data) => {
					if (disabled) return;

					const serviceCaseToSend = serviceCaseId
						? { id: serviceCaseId }
						: shouldCreateServiceCase && data.serviceCaseNote && data.serviceCaseType
							? { note: data.serviceCaseNote, type: data.serviceCaseType }
							: undefined;

					createDefect(
						{
							note: data.note,
							files: data.files.map((file) => ({ id: file })),
							defectType: { id: data.defectType },
							source: data.source,
							...(serviceCaseToSend && { serviceCase: serviceCaseToSend }),
							...(warrantyClaimId && { warrantyClaim: { id: warrantyClaimId } }),
						},
						{
							onSuccess: (data) => {
								invalidateProduct();
								invalidateDefects();
								invalidateAllProductDefects();
								onSuccess?.(data._data);
								toast.success('Vada vytvořena');
								if (data._data.serviceCaseId && serviceCaseData && 'shouldRedirect' in serviceCaseData) {
									router.push(`/service/${data._data.serviceCaseId}`);
								}
							},
						},
					);
				}}
			>
				{(control) => (
					<Stack gap={4}>
						{shouldCreateServiceCase && (
							<ComboboxControl
								control={control}
								name="serviceCaseType"
								label="Typ servisu"
								items={SERVICE_CASE_TYPE_NAMES.map((type) => ({
									value: type,
									label: ServiceCaseTypeMessage[type],
								}))}
							/>
						)}

						<ComboboxControl
							control={control}
							name="defectType"
							label="Typ závady"
							onSearchChange={updateSearchTerm}
							{...lazyComboboxProps}
							disabled={disabled}
						/>

						<TextControl control={control} name="note" label="Popis závady" rows={4} disabled={disabled} />

						<TemporaryFileUploader
							inputName="files"
							pageType="defect"
							entityCode={formatProductCode(product.code)}
							hideSubmitButton={true}
							isUploadingForm={isUploadingForm}
							isUploadingFile={isUploadingFile}
							toggleUploadingFile={toggleUploadingFile}
							disabled={disabled}
						/>

						<Button type="submit" isLoading={isUploadingFile || isStillLoading} disabled={disabled}>
							Vytvořit
						</Button>
					</Stack>
				)}
			</FormContext>
		</ProductDefectFormCard>
	);
};
