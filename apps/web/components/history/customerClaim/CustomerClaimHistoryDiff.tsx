import { CustomerClaimStatusMessage } from '@pocitarna-nx-2023/config';
import type { customerClaim } from '@pocitarna-nx-2023/zodios';
import { type FC } from 'react';
import type { z } from 'zod';
import { CustomerClaimCodeRenderer } from '../CustomerClaimCodeRenderer';
import { type KeyDiffMap, KeyDiffViewer } from '../KeyDiffViewer';
import { UserRenderer } from '../UserRenderer';

type Props = {
	current?: z.infer<typeof customerClaim>;
	previous?: z.infer<typeof customerClaim>;
};

const keyDiffMap: KeyDiffMap<z.infer<typeof customerClaim>> = {
	createdAt: {
		label: 'Vytvořeno',
	},
	createdById: {
		label: 'Vytvořil',
		renderer: UserRenderer,
	},
	codeId: {
		label: 'Kód',
		renderer: CustomerClaimCodeRenderer,
	},
	status: {
		label: 'Stav',
		formatter: (value) => CustomerClaimStatusMessage[value as keyof typeof CustomerClaimStatusMessage],
	},
};

export const CustomerClaimHistoryDiff: FC<Props> = (props) => {
	return <KeyDiffViewer keyDiffMap={keyDiffMap} {...props} />;
};
