import { type ApiBody } from '@pocitarna-nx-2023/zodios';
import Link from 'next/link';
import { type FC } from 'react';
import { AttributeValuesOverviewByEnvelope } from '../AttributeValuesOverviewByEnvelope';
import { EnvelopeName } from './EnvelopeName';

type Props = {
	productEnvelope: ApiBody<'getProductEnvelope'>;
	withAttributes?: boolean;
};

export const EnvelopeRecap: FC<Props> = ({ productEnvelope, withAttributes = true }) => {
	return (
		<>
			<Link href={`/product/envelope/${productEnvelope.id}`} className="text-link">
				<EnvelopeName productEnvelope={productEnvelope} />
			</Link>
			{withAttributes && <AttributeValuesOverviewByEnvelope envelope={productEnvelope} include={['other'] as const} />}
		</>
	);
};
