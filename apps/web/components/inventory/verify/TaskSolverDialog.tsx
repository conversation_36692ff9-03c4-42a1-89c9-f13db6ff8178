import { type ProductDefectSource } from '@pocitarna-nx-2023/config';
import {
	ComboboxControl,
	Dialog,
	DialogContent,
	DialogHeader,
	DialogTitle,
	FormContext,
	MultiSelectControl,
	Stack,
	TextControl,
} from '@pocitarna-nx-2023/ui';
import { safeDivide } from '@pocitarna-nx-2023/utils';
import { type ApiBody } from '@pocitarna-nx-2023/zodios';
import { apiHooks } from '@pocitarna-nx-2023/zodios-client';
import { type FC } from 'react';
import { type z } from 'zod';
import { useCustomerClaimData } from '../../../hooks/useCustomerClaimData';
import { useLazyComboboxProps } from '../../../hooks/useLazyComboboxProps';
import { useSearchFilter } from '../../../hooks/useSearchFilter';
import { useServiceCaseData } from '../../../hooks/useServiceCaseData';
import { useServiceTaskResolutionScope } from '../../../hooks/useServiceTaskResolutionScope';
import { useWarrantyClaimData } from '../../../hooks/useWarrantyClaimData';
import { onInventoryItemSuccess } from '../../../utils/inventory';
import { DefectResolutionButtons } from '../../productDefect/DefectResolutionButtons';
import { defectResolutionSchema } from '../../productDefect/ProductDefectSolver';
import { ServiceAttributeValues } from '../../productDefect/ServiceAttributeValues';
import { ServiceTaskPriceInput } from '../../productDefect/ServiceTaskPriceInput';

export const ServiceCaseSolvingDialog: FC<{
	serviceCase: ApiBody<'getServiceCase'>;
	open: boolean;
	onOpenChange: (open: boolean) => void;
	inventoryId: string;
	inventoryItem: ApiBody<'getInventoryItems'>[number];
}> = ({ serviceCase, ...rest }) => {
	const { productDefects } = useServiceCaseData(serviceCase.id, 'all-affected-products');

	return (
		<TaskSolverDialog
			title="Řešit vady"
			source="SERVICE_CASE"
			productDefects={productDefects}
			serviceCaseId={serviceCase.id}
			{...rest}
		/>
	);
};

export const WarrantyClaimSolvingDialog: FC<{
	warrantyClaim: ApiBody<'getWarrantyClaim'>;
	open: boolean;
	onOpenChange: (open: boolean) => void;
	inventoryId: string;
	inventoryItem: ApiBody<'getInventoryItems'>[number];
}> = ({ warrantyClaim, ...rest }) => {
	const { productDefects } = useWarrantyClaimData(warrantyClaim.id);

	return (
		<TaskSolverDialog
			title="Řešit vady"
			source="WARRANTY_CLAIM"
			productDefects={productDefects}
			warrantyClaimId={warrantyClaim.id}
			{...rest}
		/>
	);
};

export const CustomerClaimSolvingDialog: FC<{
	customerClaim: ApiBody<'getCustomerClaim'>;
	open: boolean;
	onOpenChange: (open: boolean) => void;
	inventoryId: string;
	inventoryItem: ApiBody<'getInventoryItems'>[number];
}> = ({ customerClaim, ...rest }) => {
	const { productDefects } = useCustomerClaimData(customerClaim.id, 'all-affected-products');

	return (
		<TaskSolverDialog
			title="Řešit vady"
			source="CUSTOMER_CLAIM"
			productDefects={productDefects}
			customerClaimId={customerClaim.id}
			{...rest}
		/>
	);
};

type Props = {
	open: boolean;
	onOpenChange: (open: boolean) => void;
	onSuccess?: () => void;
	title: string;
	source: ProductDefectSource;
	productDefects: ApiBody<'getAllProductDefects'>;
	customerClaimId?: string;
	serviceCaseId?: string;
	warrantyClaimId?: string;
	inventoryId: string;
	inventoryItem: ApiBody<'getInventoryItems'>[number];
};

const TaskSolverDialog: FC<Props> = ({
	title,
	source,
	productDefects,
	open,
	onOpenChange,
	customerClaimId,
	serviceCaseId,
	warrantyClaimId,
	inventoryId,
	inventoryItem,
}) => {
	const unsolvedDefects = productDefects.filter((defect) => defect.serviceTask?.status !== 'CLOSED');

	return (
		<Dialog open={open} onOpenChange={onOpenChange}>
			<DialogContent size="lg">
				<DialogHeader>
					<DialogTitle>{title}</DialogTitle>
				</DialogHeader>
				<DefectSolver
					unsolvedDefects={unsolvedDefects}
					source={source}
					customerClaimId={customerClaimId}
					serviceCaseId={serviceCaseId}
					warrantyClaimId={warrantyClaimId}
					onOpenChange={onOpenChange}
					inventoryId={inventoryId}
					inventoryItem={inventoryItem}
				/>
			</DialogContent>
		</Dialog>
	);
};

export const DefectSolver: FC<
	Omit<Props, 'title' | 'open' | 'productDefects'> & {
		unsolvedDefects: ApiBody<'getAllProductDefects'>;
		customerClaimId?: string;
		serviceCaseId?: string;
		warrantyClaimId?: string;
	}
> = ({ unsolvedDefects, customerClaimId, serviceCaseId, warrantyClaimId, onOpenChange, inventoryId, inventoryItem }) => {
	const { resolutionScope, handleResolutionScopeChange } = useServiceTaskResolutionScope();
	const { updateSearchTerm, filter: searchTermFilter } = useSearchFilter('name');

	const { invalidate: invalidateInventory } = apiHooks.useGetInventoryItems({ params: { inventoryId } }, { enabled: false });
	const { invalidate: invalidateProduct } = apiHooks.useGetProduct(
		{ params: { productId: unsolvedDefects[0]?.productId ?? '' } },
		{ enabled: false },
	);
	const { invalidate: invalidateProductAttributeValues } = apiHooks.useGetProductAttributeValues(
		{ params: { productId: unsolvedDefects[0]?.productId ?? '' } },
		{ enabled: false },
	);

	const { invalidate: invalidateProductDefects } = apiHooks.useGetAllProductDefects(undefined, { enabled: false });

	const { mutate: handleInventoryItemAction, isLoading } = apiHooks.useHandleInventoryItemAction({
		params: { inventoryId, inventoryItemId: inventoryItem.id },
	});

	const serviceTaskTypesLazyComboboxProps = useLazyComboboxProps<ApiBody<'getServiceTaskTypes'>[number]>('getServiceTaskTypes', {
		queries: { filter: { ...searchTermFilter, type: { eq: '{SERVICE}' } } },
		formatResult: (type) => ({ value: type.id, label: type.name }),
	});

	const handleSubmit = (data: z.infer<typeof defectResolutionSchema>) => {
		const individualPrice = safeDivide(data.price, data.productDefectIds.length);

		handleInventoryItemAction(
			{
				actionType: 'RESOLVE_OPEN_TASK',
				productId: unsolvedDefects[0].productId,
				productDefectsIds: data.productDefectIds,
				serviceTaskTypeId: data.serviceTaskTypeId,
				attributeValues: data.attributeValues,
				price: individualPrice,
				warrantyClaimId,
				serviceCaseId,
				customerClaimId,
				resolutionScope,
			},
			{
				onSuccess: () => {
					invalidateInventory();
					invalidateProduct();
					invalidateProductAttributeValues();
					invalidateProductDefects();
					onOpenChange(false);
					onInventoryItemSuccess(inventoryItem);
				},
			},
		);
	};

	if (unsolvedDefects.length === 0) return null;

	return (
		<FormContext
			schema={defectResolutionSchema}
			defaultValues={{ note: '', serviceTaskTypeId: '', productDefectIds: [], attributeValues: [], price: 0 }}
			onSubmit={handleSubmit}
		>
			{(control) => (
				<Stack gap={4}>
					<MultiSelectControl
						control={control}
						name="productDefectIds"
						label="Vybrat vady produktu"
						options={unsolvedDefects.map((item) => ({ value: item.id, label: item.defectType.name }))}
					/>
					<ComboboxControl
						control={control}
						name="serviceTaskTypeId"
						label="Vyřešit pomocí"
						enableFilter
						onSearchChange={updateSearchTerm}
						{...serviceTaskTypesLazyComboboxProps}
					/>

					<ServiceTaskPriceInput />
					{unsolvedDefects[0].productId && <ServiceAttributeValues productId={unsolvedDefects[0].productId} />}
					<TextControl control={control} name="note" label="Poznámka" />
					<DefectResolutionButtons
						unsolvedDefects={unsolvedDefects}
						isLoading={isLoading}
						source={warrantyClaimId ? 'WARRANTY_CLAIM' : serviceCaseId ? 'SERVICE_CASE' : 'CUSTOMER_CLAIM'}
						handleResolutionScopeChange={handleResolutionScopeChange}
					/>
				</Stack>
			)}
		</FormContext>
	);
};
