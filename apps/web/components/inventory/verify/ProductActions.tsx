import { Button, DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger, Icon } from '@pocitarna-nx-2023/ui';
import { getProductStatusesBelow } from '@pocitarna-nx-2023/utils';
import { type ApiBody } from '@pocitarna-nx-2023/zodios';
import { apiHooks } from '@pocitarna-nx-2023/zodios-client';
import { type FC } from 'react';
import { useToggle } from 'rooks';
import { AddToOrderDialog } from './AddToOrderDialog';
import { CustomerClaimDialog } from './CustomerClaimCreateDialog';
import { ProductOkDialog } from './ProductOkDialog';
import { RemoveFromOrderDialog } from './RemoveFromOrderDialog';
import { ServiceCaseCreateDialog } from './ServiceCaseCreateDialog';
import { StatusTransitionStatusDialog } from './StatusTransitionStatusDialog';
import { SwapOrderItemDialog } from './SwapOrderItemDialog';
import { CustomerClaimSolvingDialog, ServiceCaseSolvingDialog, WarrantyClaimSolvingDialog } from './TaskSolverDialog';
import { WarrantyClaimCreateDialog } from './WarrantyClaimCreateDialog';

type Props = {
	inventoryItem: ApiBody<'getInventoryItems'>[number];
};

export const ProductActions: FC<Props> = ({ inventoryItem }) => {
	const [open, toggleOpen] = useToggle();
	const [serviceDialogOpen, toggleServiceDialog] = useToggle();
	const [warrantyDialogOpen, toggleWarrantyDialog] = useToggle();
	const [customerClaimDialogOpen, toggleCustomerClaimDialog] = useToggle();
	const [orderItemRemovalDialogOpen, toggleOrderItemRemovalDialog] = useToggle();
	const [swapFoundOrderItemDialogOpen, toggleSwapFoundOrderItemDialog] = useToggle();
	const [swapNotFoundOrderItemDialogOpen, toggleSwapNotFoundOrderItemDialog] = useToggle();
	const [issueSolverDialogOpen, toggleIssueSolverDialog] = useToggle();
	const [addToOrderDialogOpen, toggleAddToOrderDialog] = useToggle();
	const [missingProductDialogOpen, toggleMissingProductDialog] = useToggle();
	const [lostProductDialogOpen, toggleLostProductDialog] = useToggle();
	const [productOkDialogOpen, toggleProductOkDialog] = useToggle();

	const { data: productData } = apiHooks.useGetProduct({ params: { productId: inventoryItem.productId } }, { enabled: open });
	const product = productData?._data ?? null;

	const { data: serviceCaseData } = apiHooks.useGetServiceCase(
		{ params: { serviceCaseId: inventoryItem.serviceCaseId ?? '' } },
		{ enabled: !!inventoryItem.serviceCaseId && open },
	);
	const serviceCase = serviceCaseData?._data ?? null;

	const { data: warrantyClaimData } = apiHooks.useGetWarrantyClaim(
		{ params: { warrantyClaimId: inventoryItem.warrantyClaimId ?? '' } },
		{ enabled: !!inventoryItem.warrantyClaimId && open },
	);
	const warrantyClaim = warrantyClaimData?._data ?? null;

	const { data: customerClaimData } = apiHooks.useGetCustomerClaim(
		{ params: { customerClaimId: inventoryItem.customerClaimId ?? '' } },
		{ enabled: !!inventoryItem.customerClaimId && open },
	);
	const customerClaim = customerClaimData?._data ?? null;

	const productIsOk = inventoryItem.inventoryStatus === 'OK';
	const productShoulNotBeInInventory = inventoryItem.inventoryStatus === 'NEW_ADDITION';
	const productWasNotFound = inventoryItem.scannedAt == null;

	return (
		<>
			<DropdownMenu open={open} onOpenChange={toggleOpen}>
				<DropdownMenuTrigger asChild>
					<Button variant="outline" size="sm">
						Akce
					</Button>
				</DropdownMenuTrigger>
				<DropdownMenuContent align="start">
					<DropdownMenuItem
						onSelect={toggleServiceDialog}
						disabled={!productIsOk || ['SERVICE', 'WARRANTY_CLAIM', 'CUSTOMER_CLAIM'].includes(inventoryItem.status)}
					>
						<Icon name="wrench" />
						<span className="ml-2">Poslat do servisu</span>
					</DropdownMenuItem>

					<DropdownMenuItem
						onSelect={toggleWarrantyDialog}
						disabled={!productIsOk || ['SERVICE', 'WARRANTY_CLAIM', 'CUSTOMER_CLAIM'].includes(inventoryItem.status)}
					>
						<Icon name="shield-exclamation" />
						<span className="ml-2">Reklamovat dodavateli</span>
					</DropdownMenuItem>

					<DropdownMenuItem
						onSelect={toggleCustomerClaimDialog}
						disabled={
							!productShoulNotBeInInventory || ['SERVICE', 'WARRANTY_CLAIM', 'CUSTOMER_CLAIM'].includes(inventoryItem.status)
						}
					>
						<Icon name="bullhorn" />
						<span className="ml-2">Nová zákaznická reklamace</span>
					</DropdownMenuItem>

					<DropdownMenuItem
						onSelect={toggleOrderItemRemovalDialog}
						disabled={!productShoulNotBeInInventory || !inventoryItem.ecommerceOrderId}
					>
						<Icon name="trash" />
						<span className="ml-2">Odstranit z objednávky</span>
					</DropdownMenuItem>

					<DropdownMenuItem
						onSelect={toggleSwapFoundOrderItemDialog}
						disabled={!productShoulNotBeInInventory || !inventoryItem.ecommerceOrderId}
					>
						<Icon name="arrow-right" />
						<span className="ml-2">Nahradit produkt za produkt co není skladem</span>
					</DropdownMenuItem>

					<DropdownMenuItem
						onSelect={toggleSwapNotFoundOrderItemDialog}
						disabled={!productWasNotFound || !inventoryItem.ecommerceOrderId}
					>
						<Icon name="arrow-right" />
						<span className="ml-2">Vyměnit produkt za produkt skladem</span>
					</DropdownMenuItem>

					<DropdownMenuItem
						onSelect={toggleIssueSolverDialog}
						disabled={!serviceCase || !productWasNotFound || serviceCase.status === 'CLOSED'}
					>
						<Icon name="check-circle" />
						<span className="ml-2">Uzavřít servis</span>
					</DropdownMenuItem>

					<DropdownMenuItem
						onSelect={toggleIssueSolverDialog}
						disabled={!warrantyClaim || !productWasNotFound || warrantyClaim.status === 'CLOSED'}
					>
						<Icon name="check-circle" />
						<span className="ml-2">Uzavřít dod. reklamaci</span>
					</DropdownMenuItem>

					<DropdownMenuItem
						onSelect={toggleIssueSolverDialog}
						disabled={!customerClaim || !productWasNotFound || customerClaim.status === 'CLOSED'}
					>
						<Icon name="check-circle" />
						<span className="ml-2">Uzavřít zak. reklamaci</span>
					</DropdownMenuItem>

					<DropdownMenuItem
						onSelect={toggleAddToOrderDialog}
						disabled={!productWasNotFound || inventoryItem.ecommerceOrderId != null}
					>
						<Icon name="plus" />
						<span className="ml-2">Přidat do objednávky</span>
					</DropdownMenuItem>

					<DropdownMenuItem
						onSelect={toggleMissingProductDialog}
						disabled={!productWasNotFound || !getProductStatusesBelow('TESTED').includes(inventoryItem.status)}
					>
						<Icon name="exclamation-circle" />
						<span className="ml-2">Označit produkt jako chybějící</span>
					</DropdownMenuItem>

					<DropdownMenuItem onSelect={toggleLostProductDialog} disabled={!productWasNotFound}>
						<Icon name="box-open" />
						<span className="ml-2">Označit produkt jako ztracený ve skladu</span>
					</DropdownMenuItem>

					<DropdownMenuItem onSelect={toggleProductOkDialog} disabled={productIsOk}>
						<Icon name="check" />
						<span className="ml-2">Označit produkt jako OK</span>
					</DropdownMenuItem>
				</DropdownMenuContent>
			</DropdownMenu>

			{product && (
				<ServiceCaseCreateDialog
					product={product}
					open={serviceDialogOpen}
					onOpenChange={toggleServiceDialog}
					inventoryId={inventoryItem.inventoryId}
					inventoryItem={inventoryItem}
				/>
			)}
			{product && (
				<WarrantyClaimCreateDialog
					product={product}
					open={warrantyDialogOpen}
					onOpenChange={toggleWarrantyDialog}
					inventoryId={inventoryItem.inventoryId}
					inventoryItem={inventoryItem}
				/>
			)}
			{product && (
				<CustomerClaimDialog
					inventoryId={inventoryItem.inventoryId}
					inventoryItem={inventoryItem}
					product={product}
					open={customerClaimDialogOpen}
					onOpenChange={toggleCustomerClaimDialog}
				/>
			)}
			{inventoryItem.ecommerceOrderId && (
				<RemoveFromOrderDialog
					orderId={inventoryItem.ecommerceOrderId}
					productId={inventoryItem.productId}
					open={orderItemRemovalDialogOpen}
					onOpenChange={toggleOrderItemRemovalDialog}
					inventoryId={inventoryItem.inventoryId}
					inventoryItem={inventoryItem}
				/>
			)}
			{inventoryItem.ecommerceOrderId && productShoulNotBeInInventory && product && (
				<SwapOrderItemDialog
					orderId={inventoryItem.ecommerceOrderId}
					inventoryItem={inventoryItem}
					product={product}
					open={swapFoundOrderItemDialogOpen}
					onOpenChange={toggleSwapFoundOrderItemDialog}
					inventoryId={inventoryItem.inventoryId}
					variant="item-found"
				/>
			)}
			{inventoryItem.ecommerceOrderId && productWasNotFound && product && (
				<SwapOrderItemDialog
					orderId={inventoryItem.ecommerceOrderId}
					inventoryItem={inventoryItem}
					product={product}
					open={swapNotFoundOrderItemDialogOpen}
					onOpenChange={toggleSwapNotFoundOrderItemDialog}
					inventoryId={inventoryItem.inventoryId}
					variant="item-not-found"
				/>
			)}
			{customerClaim && productWasNotFound && (
				<CustomerClaimSolvingDialog
					customerClaim={customerClaim}
					open={issueSolverDialogOpen}
					onOpenChange={toggleIssueSolverDialog}
					inventoryId={inventoryItem.inventoryId}
					inventoryItem={inventoryItem}
				/>
			)}
			{serviceCase && productWasNotFound && (
				<ServiceCaseSolvingDialog
					serviceCase={serviceCase}
					open={issueSolverDialogOpen}
					onOpenChange={toggleIssueSolverDialog}
					inventoryId={inventoryItem.inventoryId}
					inventoryItem={inventoryItem}
				/>
			)}
			{warrantyClaim && productWasNotFound && (
				<WarrantyClaimSolvingDialog
					warrantyClaim={warrantyClaim}
					open={issueSolverDialogOpen}
					onOpenChange={toggleIssueSolverDialog}
					inventoryId={inventoryItem.inventoryId}
					inventoryItem={inventoryItem}
				/>
			)}
			{productWasNotFound && !inventoryItem.ecommerceOrderId && product && (
				<AddToOrderDialog
					inventoryId={inventoryItem.inventoryId}
					inventoryItem={inventoryItem}
					product={product}
					open={addToOrderDialogOpen}
					onOpenChange={toggleAddToOrderDialog}
				/>
			)}
			<StatusTransitionStatusDialog
				productId={inventoryItem.productId}
				open={missingProductDialogOpen}
				onOpenChange={toggleMissingProductDialog}
				inventoryId={inventoryItem.inventoryId}
				inventoryItem={inventoryItem}
				status="MISSING"
			/>
			<StatusTransitionStatusDialog
				productId={inventoryItem.productId}
				open={lostProductDialogOpen}
				onOpenChange={toggleLostProductDialog}
				inventoryId={inventoryItem.inventoryId}
				inventoryItem={inventoryItem}
				status="LOST_FROM_INVENTORY"
			/>
			<ProductOkDialog
				open={productOkDialogOpen}
				onOpenChange={toggleProductOkDialog}
				inventoryId={inventoryItem.inventoryId}
				inventoryItem={inventoryItem}
			/>
		</>
	);
};
