import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@pocitarna-nx-2023/ui';
import type { ApiBody } from '@pocitarna-nx-2023/zodios';
import { apiHooks } from '@pocitarna-nx-2023/zodios-client';
import { type FC } from 'react';
import { onInventoryItemSuccess } from '../../../utils/inventory';
import { AddProductDefect } from '../../productDefect/AddProductDefect';

type Props = {
	product: ApiBody<'getProduct'>;
	inventoryId: string;
	inventoryItem: ApiBody<'getInventoryItems'>[number];
	open: boolean;
	onOpenChange: (open: boolean) => void;
};

export const CustomerClaimDialog: FC<Props> = ({ inventoryId, inventoryItem, product, open, onOpenChange }) => {
	return (
		<Dialog open={open} onOpenChange={onOpenChange}>
			<DialogContent size="lg">
				<DialogHeader>
					<DialogTitle>Nová zákaznická reklamace</DialogTitle>
				</DialogHeader>
				<CustomerClaimCreateForm
					product={product}
					inventoryId={inventoryId}
					inventoryItem={inventoryItem}
					onOpenChange={onOpenChange}
				/>
			</DialogContent>
		</Dialog>
	);
};

export const CustomerClaimCreateForm: FC<Omit<Props, 'open'>> = ({ inventoryId, inventoryItem, product, onOpenChange }) => {
	const { invalidate: invalidateProductDefects } = apiHooks.useGetProductDefects(
		{
			params: { productId: product.id },
		},
		{ enabled: false },
	);
	const { invalidate: invalidateCustomerClaims } = apiHooks.useGetCustomerClaims({}, { enabled: false });
	const { invalidate: invalidateProducts } = apiHooks.useGetAllProducts({}, { enabled: false });
	const { invalidate: invalidateProduct } = apiHooks.useGetProduct({ params: { productId: product.id } }, { enabled: false });
	const { invalidate: invalidateInventory } = apiHooks.useGetInventoryItems({ params: { inventoryId } }, { enabled: false });

	const { mutate: handleInventoryItemAction, isLoading } = apiHooks.useHandleInventoryItemAction({
		params: { inventoryId, inventoryItemId: inventoryItem.id },
	});

	const onDefectCreation = async (productDefect: ApiBody<'createProductDefect'>) => {
		handleInventoryItemAction(
			{
				actionType: 'CREATE_CUSTOMER_CLAIM',
				productId: product.id,
				productDefectId: productDefect.id,
			},
			{
				onSuccess: () => {
					onOpenChange(false);
					invalidateInventory();
					invalidateProducts();
					invalidateProduct();
					invalidateCustomerClaims();
					invalidateProductDefects();
					onInventoryItemSuccess(inventoryItem);
				},
			},
		);
	};

	return <AddProductDefect product={product} onSuccess={onDefectCreation} source="CUSTOMER_CLAIM" isStillLoading={isLoading} />;
};
