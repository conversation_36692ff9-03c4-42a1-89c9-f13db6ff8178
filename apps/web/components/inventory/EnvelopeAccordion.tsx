import { NOT_AVAILABLE } from '@pocitarna-nx-2023/config';
import { cn, Icon, TableCell, TableRow } from '@pocitarna-nx-2023/ui';
import { type ApiBody } from '@pocitarna-nx-2023/zodios';
import { cva, type VariantProps } from 'class-variance-authority';
import Link from 'next/link';
import { type FC, useEffect, useRef } from 'react';
import { useToggle } from 'rooks';
import { type InventorySelection } from '../../pages/inventory/[inventoryId]';
import { EnvelopeTable } from './EnvelopeTable';

type Props = {
	inventory: ApiBody<'getInventory'>;
	inventoryItems: ApiBody<'getInventoryItems'>;
	selection?: InventorySelection;
	shouldAutoExpand?: boolean;
};

const rowVariants = cva('cursor-pointer', {
	variants: {
		variant: {
			destructive: 'bg-red-100 hover:bg-red-200',
			warning: 'bg-yellow-100 hover:bg-yellow-200',
			success: 'bg-green-100 hover:bg-green-200',
			info: 'bg-blue-100 hover:bg-blue-200',
		},
	},
});

type Variants = VariantProps<typeof rowVariants>['variant'];

const cellVariants = cva('', {
	variants: {
		variant: {
			destructive: 'border-red-400 text-destructive',
			warning: 'border-yellow-400 text-warning',
			success: 'border-green-400 text-success',
			info: 'border-blue-400 text-info',
		},
	},
});

export const EnvelopeAccordion: FC<Props> = ({ inventory, inventoryItems, shouldAutoExpand = false, selection }) => {
	const { productEnvelopeId, productEnvelopeCode, productEnvelopeName } = inventoryItems[0];
	const [open, toggleOpen] = useToggle(shouldAutoExpand);
	const variant = getEnvelopeVariant(inventoryItems);
	const accordionRef = useRef<HTMLTableRowElement>(null);

	useEffect(() => {
		if (shouldAutoExpand && accordionRef.current) {
			accordionRef.current.scrollIntoView({ behavior: 'smooth', block: 'center' });
		}
	}, [shouldAutoExpand]);

	return (
		<>
			<TableRow ref={accordionRef} className={rowVariants({ variant })} onClick={toggleOpen}>
				<TableCell className={cn(cellVariants({ variant }), 'w-1')}>
					{productEnvelopeCode ? (
						<Link href={`/product/envelope/${productEnvelopeId}`} className="underline hover:no-underline" target="_blank">
							{productEnvelopeCode}
						</Link>
					) : (
						NOT_AVAILABLE
					)}
				</TableCell>
				<TableCell className={cellVariants({ variant })}>{productEnvelopeName ?? 'Produkty bez přidělené karty'}</TableCell>
				<TableCell className={cn(cellVariants({ variant }), 'text-right')}>
					<span className={cellVariants({ variant: 'success' })}>
						{inventoryItems.filter((item) => item.inventoryStatus === 'OK').length}
					</span>{' '}
					|{' '}
					<span className={cellVariants({ variant: 'warning' })}>
						{inventoryItems.filter((item) => item.scannedAt == null && item.inventoryStatus !== 'OK').length}
					</span>{' '}
					|{' '}
					<span className={cellVariants({ variant: 'destructive' })}>
						{inventoryItems.filter((item) => item.inventoryStatus === 'ERROR').length}
					</span>{' '}
					|{' '}
					<span className={cellVariants({ variant: 'info' })}>
						{inventoryItems.filter((item) => item.inventoryStatus === 'NEW_ADDITION').length}
					</span>
				</TableCell>
				<TableCell className={cn(cellVariants({ variant }), 'text-right w-1')}>
					<Icon name={open ? 'chevron-up' : 'chevron-down'} />
				</TableCell>
			</TableRow>
			<TableRow>
				{open && (
					<TableCell colSpan={4} className="!p-0 border-0">
						<EnvelopeTable inventory={inventory} inventoryItems={inventoryItems} selection={selection} />
					</TableCell>
				)}
			</TableRow>
		</>
	);
};

const getEnvelopeVariant = (inventoryItems: ApiBody<'getInventoryItems'>): Variants => {
	if (inventoryItems.every((item) => item.inventoryStatus === 'OK')) return 'success';
	if (inventoryItems.every((item) => item.scannedAt == null || item.inventoryStatus === 'ERROR')) return 'destructive';
	if (inventoryItems.some((item) => item.inventoryStatus === 'ERROR' || item.scannedAt == null)) return 'warning';
	if (inventoryItems.some((item) => item.inventoryStatus === 'NEW_ADDITION')) return 'info';
	return undefined;
};
