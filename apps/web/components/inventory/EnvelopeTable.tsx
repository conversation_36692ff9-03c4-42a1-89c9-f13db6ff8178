import { EMPTY_VALUE, InventoryItemStatusMessage } from '@pocitarna-nx-2023/config';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@pocitarna-nx-2023/ui';
import { formatCustomerClaimCode, formatServiceCaseCode, formatWarrantyClaimCode } from '@pocitarna-nx-2023/utils';
import { type ApiBody } from '@pocitarna-nx-2023/zodios';
import Link from 'next/link';
import { type FC, useEffect, useRef } from 'react';
import { type InventorySelection } from '../../pages/inventory/[inventoryId]';
import { SplitCodeDisplay } from '../SplitCodeDisplay';
import { ProductActions } from './verify/ProductActions';

type Props = {
	inventory: ApiBody<'getInventory'>;
	inventoryItems: ApiBody<'getInventoryItems'>;
	selection?: InventorySelection;
};

export const EnvelopeTable = ({ inventory, inventoryItems, selection }: Props) => {
	return (
		<Table inCard>
			<TableHeader>
				<TableRow>
					<TableHead className="w-1">PCN</TableHead>
					<TableHead>SN</TableHead>
					<TableHead>Stav</TableHead>
					<TableHead>Servis</TableHead>
					<TableHead>Dod. reklamace</TableHead>
					<TableHead>Zák. reklamace</TableHead>
					<TableHead>Objednávka</TableHead>
					<TableHead>Pozice</TableHead>
					{inventory.status !== 'CLOSED' && <TableHead className="w-1" />}
				</TableRow>
			</TableHeader>
			<TableBody>
				{inventoryItems.map((item) => (
					<EnvelopeTableRow
						key={item.id}
						inventory={inventory}
						inventoryItem={item}
						isSelected={selection?.selectedProduct === item.productId}
					/>
				))}
			</TableBody>
		</Table>
	);
};

const EnvelopeTableRow: FC<{
	inventory: ApiBody<'getInventory'>;
	inventoryItem: ApiBody<'getInventoryItems'>[number];
	isSelected?: boolean;
}> = ({ inventory, inventoryItem, isSelected }) => {
	const rowRef = useRef<HTMLTableRowElement>(null);
	const statusClassName = getStatusClassName(inventoryItem);
	const { preScanWarehousePositionId, preScanWarehousePositionName, postScanWarehousePositionId, postScanWarehousePositionName } =
		inventoryItem;
	const positionId = postScanWarehousePositionId ?? preScanWarehousePositionId;
	const positionName = postScanWarehousePositionName ?? preScanWarehousePositionName;
	const positionClassName = preScanWarehousePositionId
		? getPositionClassName(preScanWarehousePositionId, postScanWarehousePositionId)
		: undefined;

	useEffect(() => {
		if (isSelected && rowRef.current) {
			setTimeout(() => {
				rowRef.current?.scrollIntoView({ behavior: 'smooth', block: 'center' });
			}, 300); // Delay to allow accordion to open
		}
	}, [isSelected]);

	return (
		<TableRow ref={rowRef} className={isSelected ? '!bg-amber-50' : ''}>
			<TableCell className="w-1">
				<Link className="text-link" href={`/product/${inventoryItem.productId}`} target="_blank">
					{inventoryItem.code}
				</Link>
			</TableCell>
			<TableCell>{inventoryItem.sn ? <SplitCodeDisplay code={inventoryItem.sn} /> : EMPTY_VALUE}</TableCell>
			<TableCell className={statusClassName}>{InventoryItemStatusMessage[inventoryItem.inventoryStatus]}</TableCell>
			<TableCell>
				{inventoryItem.serviceCaseCode ? (
					<Link className="text-link" href={`/service/${inventoryItem.serviceCaseId}`} target="_blank">
						{formatServiceCaseCode({ code: inventoryItem.serviceCaseCode })}
					</Link>
				) : (
					EMPTY_VALUE
				)}
			</TableCell>

			<TableCell>
				{inventoryItem.warrantyClaimCode ? (
					<Link className="text-link" href={`/warranty-claim/${inventoryItem.warrantyClaimId}`} target="_blank">
						{formatWarrantyClaimCode({ code: inventoryItem.warrantyClaimCode })}
					</Link>
				) : (
					EMPTY_VALUE
				)}
			</TableCell>

			<TableCell>
				{inventoryItem.customerClaimCode ? (
					<Link className="text-link" href={`/customer-claim/${inventoryItem.customerClaimId}`} target="_blank">
						{formatCustomerClaimCode({ code: inventoryItem.customerClaimCode })}
					</Link>
				) : (
					EMPTY_VALUE
				)}
			</TableCell>

			<TableCell>
				{inventoryItem.ecommerceOrderCode ? (
					<Link className="text-link" href={`/order/${inventoryItem.ecommerceOrderId}`} target="_blank">
						{inventoryItem.ecommerceOrderCode}
					</Link>
				) : (
					EMPTY_VALUE
				)}
			</TableCell>

			<TableCell>
				{positionId ? (
					<Link className={positionClassName} href={`/warehouse/position/${positionId}`} target="_blank">
						{positionName}
					</Link>
				) : (
					EMPTY_VALUE
				)}
			</TableCell>

			{inventory.status !== 'CLOSED' && (
				<TableCell className="w-1 text-right">
					<ProductActions inventoryItem={inventoryItem} />
				</TableCell>
			)}
		</TableRow>
	);
};

const getStatusClassName = (inventoryItem: ApiBody<'getInventoryItems'>[number]) => {
	if (inventoryItem.inventoryStatus === 'OK') return 'text-success';
	if (inventoryItem.inventoryStatus === 'ERROR') return 'text-destructive';
	if (inventoryItem.inventoryStatus === 'NEW_ADDITION') return 'text-info';
	return undefined;
};

const getPositionClassName = (preScanWarehousePositionId: string, postScanWarehousePositionId: string | null) => {
	if (!postScanWarehousePositionId) return 'text-muted-foreground';
	if (preScanWarehousePositionId === postScanWarehousePositionId) return 'text-success';
	return 'text-info';
};
