import { MultiSelect<PERSON>ontrol, Stack, toast } from '@pocitarna-nx-2023/ui';
import { type ApiBody } from '@pocitarna-nx-2023/zodios';
import { apiHooks } from '@pocitarna-nx-2023/zodios-client';
import { type FC, useCallback, useMemo } from 'react';
import { useFormContext } from 'react-hook-form';
import { CosmeticDefectCard } from './CosmeticDefectCard';

type Props = {
	cosmeticAreaId: string;
	productId: string;
	areaCosmeticDefects: ApiBody<'getCosmeticDefects'>;
	productCosmeticDefectsByArea: ApiBody<'getProductCosmeticDefects'>;
	isFix?: boolean;
};

export const CosmeticDefectByAreaHandling: FC<Props> = ({
	cosmeticAreaId,
	productId,
	areaCosmeticDefects,
	productCosmeticDefectsByArea,
	isFix = false,
}) => {
	const { control } = useFormContext<{ cosmeticDefects: Record<string, string[]> }>();
	const { mutate } = apiHooks.useAssignCosmeticDefectsToProductArea({ params: { productId, cosmeticAreaId } });
	const { invalidate: invalidateProductCosmeticDefects } = apiHooks.useGetProductCosmeticDefects(
		{ queries: { filter: { productId: { eq: productId } } } },
		{ enabled: false },
	);

	const valueName = useMemo(() => `cosmeticDefects.${cosmeticAreaId}` as const, [cosmeticAreaId]);

	const onChange = useCallback(
		(cosmeticDefectIds: string[]) => {
			mutate(
				{ cosmeticDefectIds: cosmeticDefectIds, isFix },
				{
					onSuccess: () => {
						invalidateProductCosmeticDefects();
						toast.success('Zaktualizováno');
					},
				},
			);
		},
		[invalidateProductCosmeticDefects, mutate, isFix],
	);

	return (
		<Stack gap={4}>
			<MultiSelectControl
				control={control}
				name={valueName}
				onChange={onChange}
				options={areaCosmeticDefects.map((item) => ({ value: item.id, label: item.name }))}
			/>
			{productCosmeticDefectsByArea.map((item) => (
				<CosmeticDefectCard key={item.id} productCosmeticDefect={item} />
			))}
		</Stack>
	);
};
