import { MAX_POSITIVE_INTEGER, TEN_SECONDS } from '@pocitarna-nx-2023/config';
import { Media, ParamList, Stack } from '@pocitarna-nx-2023/ui';
import { type ApiBody } from '@pocitarna-nx-2023/zodios';
import { apiHooks } from '@pocitarna-nx-2023/zodios-client';
import { type FC } from 'react';
import { DialogQRUploadPhoto } from '../DialogQRUploadPhoto';
import { FileUploader } from '../FileUploader';

type Props = {
	productCosmeticDefect: ApiBody<'getProductCosmeticDefects'>[number];
	disabled?: boolean;
	variant?: 'public' | 'qr-only' | 'full';
};

export const CosmeticDefectFilesCard: FC<Props> = ({ productCosmeticDefect, disabled = false, variant }) => {
	const isPublic = variant === 'public';

	const { invalidate: invalidateProductCosmeticDefects } = apiHooks.useGetProductCosmeticDefects(
		{
			queries: {
				filter: {
					productId: {
						eq: productCosmeticDefect.productId,
					},
				},
			},
		},
		{ enabled: false },
	);

	const onSuccess = () => {
		invalidateProductCosmeticDefects();
		invalidateDefectFiles();
	};

	const { data: defectFilesData, invalidate: invalidateDefectFiles } = apiHooks.useGetProductCosmeticDefectFiles(
		{
			params: { productCosmeticDefectId: productCosmeticDefect.id },
			queries: { page: 1, limit: MAX_POSITIVE_INTEGER },
		},
		{ refetchInterval: TEN_SECONDS },
	);
	const { mutateAsync: addFilesToDefect, isLoading: isAddingFiles } = apiHooks.useAddFilesToProductCosmeticDefect(
		{ params: { productCosmeticDefectId: productCosmeticDefect.id } },
		{ onSuccess },
	);
	const { mutate: deleteFiles, isLoading: isDeletingFiles } = apiHooks.useDeleteFiles(undefined, {
		onSuccess,
	});
	const { mutate: rotateImageFile, isLoading: isRotatingFile } = apiHooks.useRotateImageFile(undefined, {
		onSuccess,
	});
	const { mutate: updateSequence } = apiHooks.useUpdateProductCosmeticDefectFileSequence(
		{ params: { productCosmeticDefectId: productCosmeticDefect.id } },
		{ onSuccess },
	);

	const defectFiles = defectFilesData?._data ?? [];

	const mediaComponent = (
		<ParamList lineBreak={true} className="empty:hidden">
			<Media
				files={defectFiles.map(({ file }) => file)}
				moveTo={!disabled ? (file, sequence) => updateSequence({ fileId: file.id, sequence }) : undefined}
				onDelete={disabled ? undefined : (file) => deleteFiles({ ids: [file.id] })}
				onRotation={disabled ? undefined : (file, rotation) => rotateImageFile({ fileId: file.id, rotation })}
				isLoading={isAddingFiles || isDeletingFiles || isRotatingFile}
			/>
		</ParamList>
	);

	if (variant === 'qr-only') {
		return (
			<Stack gap={4}>
				{mediaComponent}
				<DialogQRUploadPhoto
					usage="detail"
					entityCode={productCosmeticDefect.cosmeticDefect.name}
					entityId={productCosmeticDefect.id}
					entityType="productCosmeticDefect"
				/>
			</Stack>
		);
	}

	return (
		<FileUploader
			inputName="files"
			pageType="detail"
			entityCode={productCosmeticDefect.cosmeticDefect.name}
			entityId={productCosmeticDefect.id}
			entityType="productCosmeticDefect"
			isAddingFiles={isAddingFiles}
			addFilesToEntity={addFilesToDefect}
			disabled={isPublic}
		>
			{mediaComponent}
		</FileUploader>
	);
};
