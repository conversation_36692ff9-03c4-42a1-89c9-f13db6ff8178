import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>er, <PERSON><PERSON><PERSON><PERSON>, ParamI<PERSON>, ParamList, Stack } from '@pocitarna-nx-2023/ui';
import { type ApiBody } from '@pocitarna-nx-2023/zodios';
import { type FC } from 'react';
import { CosmeticDefectFilesCard } from './CosmeticDefectFilesCard';

type Props = {
	productCosmeticDefect: ApiBody<'getProductCosmeticDefects'>[number];
	disabled?: boolean;
	variant?: 'qr-only' | 'public' | 'full';
};

export const CosmeticDefectCard: FC<Props> = ({ productCosmeticDefect, disabled = false, variant = 'qr-only' }) => {
	return (
		<Card>
			<CardHeader>
				<CardTitle>{productCosmeticDefect.cosmeticDefect.name}</CardTitle>
			</CardHeader>
			<CardContent>
				<Stack gap={4}>
					<ParamList>
						<ParamItem label="Stav">{productCosmeticDefect.cosmeticDefect.grade.name}</ParamItem>
					</ParamList>
					{productCosmeticDefect.cosmeticDefect.pictureRequired && (
						<CosmeticDefectFilesCard productCosmeticDefect={productCosmeticDefect} variant={variant} disabled={disabled} />
					)}
				</Stack>
			</CardContent>
		</Card>
	);
};
