import {
	Button,
	ComboboxControl,
	FormContext,
	type FormContextRef,
	Stack,
	Table,
	TableBody,
	TableCell,
	TableFooter,
	TableRow,
	toast,
	useDialog,
} from '@pocitarna-nx-2023/ui';
import { type ApiBody } from '@pocitarna-nx-2023/zodios';
import { apiHooks } from '@pocitarna-nx-2023/zodios-client';
import { type FC, memo, type PropsWithChildren, useEffect, useRef } from 'react';
import { useFormContext } from 'react-hook-form';
import { type z } from 'zod';
import { useCosmeticDefectsFormData } from '../../hooks/useCosmeticDefectsFormData';
import { useUserHasScope } from '../../hooks/useUserHasScope';
import { CosmeticDefectByAreaHandling } from './CosmeticDefectByAreaHandling';

type Props = {
	product: ApiBody<'getProduct'>;
	isFix?: boolean;
};

export const TestedProductCosmeticDefectsHandling: FC<Props> = ({ product, isFix }) => {
	const { defaultValues, schema, allRelevantDefectsHaveImage } = useCosmeticDefectsFormData(product);
	const formRef = useRef<FormContextRef<typeof schema>>(null);

	const { invalidate: invalidateProduct } = apiHooks.useGetProduct({ params: { productId: product.id } }, { enabled: false });
	const { mutate: saveProductTest, isLoading } = apiHooks.useSaveProductTest({ params: { productId: product.id } });
	const { closeDialog } = useDialog();

	useEffect(() => {
		formRef?.current?.reset(defaultValues);
	}, [defaultValues]);

	return (
		<FormContext
			schema={schema}
			defaultValues={defaultValues}
			ref={formRef}
			onSubmit={(data) => {
				if (!allRelevantDefectsHaveImage) {
					toast.error('Nahrajte prosím fotografii pro všechny relevantní kosmetické vady.');
					return;
				}

				saveProductTest(
					{
						gradeId: data.gradeId,
					},
					{
						onSuccess: () => {
							invalidateProduct();
							toast.success('Zaktualizováno');
							closeDialog();
						},
					},
				);
			}}
		>
			{() => (
				<CosmeticDefectsForm product={product} isFix={isFix}>
					<Button className="w-full" variant="default" type="submit" isLoading={isLoading}>
						Uložit
					</Button>
				</CosmeticDefectsForm>
			)}
		</FormContext>
	);
};

export const CosmeticDefectsForm: FC<PropsWithChildren<Props>> = memo(({ product, isFix, children }) => {
	const isTestLead = useUserHasScope('productTestLead');
	const {
		cosmeticAreas,
		productCosmeticDefects,
		rankedGrades,
		categoryCosmeticDefects,
		schema: cosmetiDefectsSchema,
	} = useCosmeticDefectsFormData(product);

	const { control } = useFormContext<z.input<typeof cosmetiDefectsSchema>>();

	return (
		<Stack gap={4}>
			<h2 className="h3">Kosmetické vady</h2>

			<Table>
				<TableBody>
					{cosmeticAreas.map((area) => {
						return (
							<TableRow key={area.id}>
								<TableCell>{area.name}</TableCell>
								<TableCell>
									<CosmeticDefectByAreaHandling
										cosmeticAreaId={area.id}
										productId={product.id}
										areaCosmeticDefects={categoryCosmeticDefects.filter((defect) =>
											defect.cosmeticAreaCosmeticDefects?.some((item) => item.cosmeticAreaId === area.id),
										)}
										productCosmeticDefectsByArea={productCosmeticDefects.filter(
											(defect) => defect.cosmeticAreaId === area.id,
										)}
										isFix={isFix}
									/>
								</TableCell>
							</TableRow>
						);
					})}
				</TableBody>
				<TableFooter>
					<TableRow>
						<TableCell>
							<strong>Stav produkutu</strong>
						</TableCell>
						<TableCell>
							<Stack gap={4}>
								<ComboboxControl
									control={control}
									name="gradeId"
									label="Stav"
									items={rankedGrades.map((grade) => ({ value: grade.id, label: grade.name }))}
									disabled={!isTestLead}
								/>
								{children}
							</Stack>
						</TableCell>
					</TableRow>
				</TableFooter>
			</Table>
		</Stack>
	);
});

CosmeticDefectsForm.displayName = 'CosmeticDefectsForm';
