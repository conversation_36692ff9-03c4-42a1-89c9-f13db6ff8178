import { type FC } from 'react';
import ReactMarkdown from 'react-markdown';
import rehypeRaw from 'rehype-raw';
import remarkGfm from 'remark-gfm';
import sanitizeHtml, { defaults as sanitizeHtmlDefaults } from 'sanitize-html';

type Props = {
	value: string;
};

export const MarkdownRenderer: FC<Props> = ({ value }) => {
	const sanitizedContent = sanitizeHtml(value, {
		allowedTags: sanitizeHtmlDefaults.allowedTags.concat(['span']),
		allowedAttributes: {
			...sanitizeHtmlDefaults.allowedAttributes,
			span: ['style'],
		},
		allowedStyles: {
			span: {
				color: [/^.*$/],
			},
		},
	});

	return (
		<div className="[&_a]:text-primary [&_a]:underline [&_a]:cursor-pointer [&_a:hover]:no-underline">
			<ReactMarkdown remarkPlugins={[remarkGfm]} rehypePlugins={[rehypeRaw]}>
				{sanitizedContent}
			</ReactMarkdown>
		</div>
	);
};
