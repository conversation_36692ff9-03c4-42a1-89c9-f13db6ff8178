import { type Grade } from '@pocitarna-nx-2023/database';
import { type ApiBody } from '@pocitarna-nx-2023/zodios';

const GRADE_NOT_FOUND = -1;

export const calculateProductGrade = ({
	product,
	defects,
	rankedGrades,
}: {
	product: ApiBody<'getProduct'>;
	defects: ApiBody<'getProductCosmeticDefects'>;
	rankedGrades: ApiBody<'getRankedGrades'>;
}): string | null => {
	const aGrade = rankedGrades.find((grade) => grade.name === 'A');
	const validGrades = product.type === 'REFURBISHED' && aGrade ? rankedGrades.slice(rankedGrades.indexOf(aGrade)) : rankedGrades;

	if (defects.length === 0) return validGrades[0]?.id ?? null;

	const sortedGrades = validGrades.map((grade) => grade.name);
	const worstGradeIndex = getWorstGradeIndex(defects, sortedGrades);

	if (worstGradeIndex === GRADE_NOT_FOUND) return null;

	const worstGrade = sortedGrades[worstGradeIndex];
	const defectCount = countDefectsAtGrade(defects, worstGrade);
	const gradeConfig = findGradeConfig(validGrades, worstGrade);

	if (!gradeConfig) {
		throw new Error(`Grade ${worstGrade} not found`);
	}

	const shouldDowngrade = defectCount > gradeConfig.maxCosmeticDefects;
	const isWorstPossibleGrade = worstGradeIndex === sortedGrades.length - 1;

	if (shouldDowngrade && !isWorstPossibleGrade) {
		const downgradedGradeName = sortedGrades[worstGradeIndex + 1];
		const downgradedGradeConfig = findGradeConfig(validGrades, downgradedGradeName);

		if (!downgradedGradeConfig) {
			throw new Error(`Downgraded grade ${downgradedGradeName} not found`);
		}

		return downgradedGradeConfig.id;
	}

	return gradeConfig.id;
};

const getWorstGradeIndex = (defects: ApiBody<'getProductCosmeticDefects'>, sortedGradeNames: Grade['name'][]): number => {
	let worstIndex = GRADE_NOT_FOUND;

	for (const defect of defects) {
		const gradeIndex = sortedGradeNames.indexOf(defect.cosmeticDefect.grade.name);

		if (gradeIndex === GRADE_NOT_FOUND) {
			throw new Error(`Unknown grade: ${defect.cosmeticDefect.grade.name}`);
		}

		worstIndex = Math.max(worstIndex, gradeIndex);
	}

	return worstIndex;
};

const countDefectsAtGrade = (defects: ApiBody<'getProductCosmeticDefects'>, gradeName: string): number => {
	return defects.filter((defect) => defect.cosmeticDefect.grade.name === gradeName).length;
};

const findGradeConfig = (grades: ApiBody<'getGrades'>, gradeName: string) => {
	return grades.find((grade) => grade.name === gradeName);
};
