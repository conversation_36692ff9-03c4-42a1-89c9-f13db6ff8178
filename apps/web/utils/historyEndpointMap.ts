import {
	CURRENCY_RATES_LAMBDA_ENDPOINT,
	type HttpMethod,
	IS_DEV,
	SHOPTET_ORDERS_LAMBDA_ENDPOINT,
	UUID_REGEX,
} from '@pocitarna-nx-2023/config';
import { type Badge } from '@pocitarna-nx-2023/ui';
import { type ComponentProps } from 'react';
import { toast } from 'sonner';

export const DEFAULT_TITLE = 'provedl akci';

const historyEndpointMap = new Map<RegExp, Record<HttpMethod[number], string>>([
	[new RegExp(`^${CURRENCY_RATES_LAMBDA_ENDPOINT}$`), { POST: 'změnil kurz várky' }],
	[new RegExp(`^${SHOPTET_ORDERS_LAMBDA_ENDPOINT}$`), { POST: 'synchronizoval objednávky' }],
	[new RegExp(`^/attribute/${UUID_REGEX}/value/${UUID_REGEX}$`), { PATCH: 'upravil hodnotu parametru' }],
	[new RegExp(`^/batch$`), { POST: 'vytvořil várku' }],
	[new RegExp(`^/batch/${UUID_REGEX}$`), { PATCH: 'upravil várku' }],
	[new RegExp(`^/batch/${UUID_REGEX}/check$`), { POST: 'zkontroloval várku' }],
	[new RegExp(`^/batch/${UUID_REGEX}/defect$`), { POST: 'přidal vadu' }],
	[new RegExp(`^/batch/${UUID_REGEX}/delivery$`), { POST: 'přijal várku' }],
	[new RegExp(`^/batch/${UUID_REGEX}/files$`), { PATCH: 'upravil soubory várky' }],
	[new RegExp(`^/batch/${UUID_REGEX}/files/${UUID_REGEX}$`), { DELETE: 'smazal soubor' }],
	[new RegExp(`^/batch/${UUID_REGEX}/products/code$`), { POST: 'přiřadil kódy k produktům' }],
	[new RegExp(`^/batch/bulk$`), { PATCH: 'upravil várky' }],
	[new RegExp(`^/batch/import$`), { POST: 'naimportoval várku' }],
	[new RegExp(`^/batch/import/product$`), { POST: 'importoval produkt' }],
	[new RegExp(`^/files/${UUID_REGEX}$`), { PATCH: 'upravil soubor' }],
	[new RegExp(`^/files/batch/${UUID_REGEX}$`), { PATCH: 'upravil soubory várky' }],
	[new RegExp(`^/files/batch-defect/${UUID_REGEX}$`), { PATCH: 'upravil soubory vady várky' }],
	[new RegExp(`^/files/product/${UUID_REGEX}$`), { PATCH: 'upravil soubory produktu' }],
	[new RegExp(`^/files/product-defect/${UUID_REGEX}$`), { PATCH: 'upravil soubory vady produktu' }],
	[new RegExp(`^/files/service-case/${UUID_REGEX}$`), { PATCH: 'upravil soubory servisního případu' }],
	[new RegExp(`^/files/vendor/${UUID_REGEX}$`), { PATCH: 'upravil soubory dodavatele' }],
	[new RegExp(`^/files/warranty-claim/${UUID_REGEX}$`), { PATCH: 'upravil soubory dod. reklamace' }],
	[new RegExp(`^/files/upload/base64$`), { POST: 'nahrál soubor' }],
	[new RegExp(`^/product$`), { POST: 'vytvořil produkt' }],
	[new RegExp(`^/product(/code)?/(${UUID_REGEX}/)?attribute-value-loader$`), { POST: 'vyčetl parametry produktu' }],
	[new RegExp(`^/product/${UUID_REGEX}$`), { PATCH: 'upravil produkt' }],
	[
		new RegExp(`^/product/${UUID_REGEX}/attribute-value$`),
		{ PATCH: 'upravil parametry produktu', POST: 'upravil parametry produktu', PUT: 'nastavil parametry produktu na detailu produktu' },
	],
	[new RegExp(`^/product/${UUID_REGEX}/attribute-value-autofill$`), { PATCH: 'potvrdil všechny šablonové parametry' }],
	[new RegExp(`^/product/${UUID_REGEX}/bulk/attribute-values$`), { PATCH: 'upravil parametry produktu na detailu várky' }],
	[new RegExp(`^/product/${UUID_REGEX}/discount$`), { POST: 'přidal slevu k produktu' }],
	[new RegExp(`^/product/${UUID_REGEX}/files$`), { PATCH: 'upravil soubory produktu' }],
	[new RegExp(`^/product/${UUID_REGEX}/files/${UUID_REGEX}$`), { DELETE: 'smazal soubor' }],
	[new RegExp(`^/product/${UUID_REGEX}/stock/attribute-values$`), { PATCH: 'upravil parametry produktu' }],
	[new RegExp(`^/product/${UUID_REGEX}/test$`), { PUT: 'otestoval produkt', PATCH: 'otestoval produkt' }],
	[new RegExp(`^/product/(${UUID_REGEX}|bulk)/test$`), { PUT: 'dokončil test produktu', PATCH: 'dokončil test produktu' }],
	[
		new RegExp(`^/product/cosmetic-defect/product/${UUID_REGEX}/cosmeticArea/${UUID_REGEX}$`),
		{ POST: 'vytvořil kosmetickou vadu produktu' },
	],
	[new RegExp(`^/product/bulk$`), { PATCH: 'upravil produkty' }],
	[new RegExp(`^/product/bulk/attribute-values$`), { PATCH: 'upravil parametry produktů na detailu várky' }],
	[new RegExp(`^/product/bulk/product-test$`), { PATCH: 'upravil informace pro test' }],
	[new RegExp(`^/product/bulk/test$`), { PATCH: 'otestoval produkty' }],
	[new RegExp(`^/product/category/${UUID_REGEX}/attribute$`), { POST: 'přidal parametr do kategorie' }],
	[new RegExp(`^/product/category/${UUID_REGEX}/attribute/${UUID_REGEX}$`), { DELETE: 'odebral parametr z kategorie' }],
	[new RegExp(`^/product/defect/${UUID_REGEX}$`), { PATCH: 'upravil vadu produktu', POST: 'vytvořil vadu produktu' }],
	[new RegExp(`^/product/defect/${UUID_REGEX}/files/${UUID_REGEX}$`), { DELETE: 'smazal soubor s vadou produktu' }],
	[new RegExp(`^/product/envelope$`), { POST: 'vytvořil kartu' }],
	[new RegExp(`^/product/envelope/${UUID_REGEX}`), { PATCH: 'upravil kartu' }],
	[new RegExp(`^/product/envelope/${UUID_REGEX}/attribute-value$`), { PUT: 'změnil parametry karty' }],
	[new RegExp(`^/product/envelope/stock$`), { POST: 'naskladnil produkty' }],
	[new RegExp(`^/product/price/bulk-update/${UUID_REGEX}$`), { PATCH: 'upravil cenu produktu' }],
	[new RegExp(`^/product/task/${UUID_REGEX}/close$`), { POST: 'uzavřel servisní zásah' }],
	[new RegExp(`^/product/task/bulk$`), { POST: 'vytvořil servisní zásahy' }],
	[new RegExp(`^/service-case$`), { POST: 'vytvořil servisní případ' }],
	[new RegExp(`^/service-case/${UUID_REGEX}$`), { PATCH: 'upravil servisní případ' }],
	[new RegExp(`^/service-case/${UUID_REGEX}/files$`), { PATCH: 'upravil soubory' }],
	[new RegExp(`^/service-case/${UUID_REGEX}/files/${UUID_REGEX}$`), { DELETE: 'smazal soubor' }],
	[new RegExp(`^/service-center$`), { POST: 'vytvořil externí servis' }],
	[new RegExp(`^/service-center/${UUID_REGEX}$`), { PATCH: 'upravil externí servis' }],
	[new RegExp(`^/service-task$`), { POST: 'vytvořil servisní zásah' }],
	[new RegExp(`^/service-task/bulk$`), { POST: 'vytvořil servisní zásah' }],
	[new RegExp(`^/service-task/${UUID_REGEX}$`), { PATCH: 'upravil servisní zásah' }],
	[new RegExp(`^/service-task/${UUID_REGEX}/close$`), { POST: 'uzavřel servisní zásah' }],
	[new RegExp(`^/shipment$`), { POST: 'založil expedici' }],
	[new RegExp(`^/shipment/${UUID_REGEX}$`), { PATCH: 'upravil expedici' }],
	[new RegExp(`^/shipment/${UUID_REGEX}/dispatch$`), { PATCH: 'odeslal produkty' }],
	[new RegExp(`^/vendor$`), { POST: 'vytvořil dodavatele' }],
	[new RegExp(`^/vendor/${UUID_REGEX}$`), { PATCH: 'upravil dodavatele' }],
	[new RegExp(`^/vendor/${UUID_REGEX}/files$`), { PATCH: 'upravil soubory dodavatele' }],
	[new RegExp(`^/vendor/${UUID_REGEX}/files/${UUID_REGEX}$`), { DELETE: 'smazal soubor' }],
	[new RegExp(`^/warehouse/position/${UUID_REGEX}/products$`), { POST: 'zaskladnil produkty', DELETE: 'vyskladnil produkt' }],
	[new RegExp(`^/warranty-claim$`), { POST: 'založil dod. reklamaci' }],
	[new RegExp(`^/warranty-claim/${UUID_REGEX}$`), { PATCH: 'upravil dod. reklamaci' }],
	[new RegExp(`^/warranty-claim/${UUID_REGEX}/files$`), { PATCH: 'upravil soubory reklamace' }],
	[new RegExp(`^/warranty-claim/${UUID_REGEX}/files/${UUID_REGEX}$`), { DELETE: 'smazal soubor reklamace' }],
	[new RegExp(`^/warranty-claim/bulk$`), { PATCH: 'upravil dod. reklamace' }],
	[new RegExp(`^/note$`), { POST: 'vytvořil poznámku' }],
	[new RegExp(`^/note/${UUID_REGEX}$`), { DELETE: 'smazal poznámku' }],
	[new RegExp(`^/customer-claim$`), { POST: 'vytvořil zákaznickou reklamaci' }],
	[new RegExp(`^/customer-claim/${UUID_REGEX}$`), { PATCH: 'upravil zákaznickou reklamaci' }],
	[new RegExp(`^/warehouse/position$`), { POST: 'vytvořil skladovou pozici' }],
	[new RegExp(`^/warehouse/position/${UUID_REGEX}$`), { PUT: 'upravil skladovou pozici' }],
	[new RegExp(`^/inventory/${UUID_REGEX}/${UUID_REGEX}$`), { PATCH: 'upravil produkt v inventurě' }],
]);

const historyEndpointKeys = Array.from(historyEndpointMap.keys());

const endpointBadgeMap = new Map<
	RegExp,
	{
		variant?: ComponentProps<typeof Badge>['variant'];
		label: string;
	}
>([
	[
		new RegExp(`^/inventory/`),
		{
			variant: 'destructive',
			label: 'Inventura',
		},
	],
]);

const endpointBadgeMapKeys = Array.from(endpointBadgeMap.keys());

const fallback = (endpoint: string, method: HttpMethod) => {
	if (IS_DEV) {
		const message = `HISTORY: No match found for: ${method} ${endpoint}`;
		console.warn(message);
		toast.warning(message);
	}

	return DEFAULT_TITLE;
};

export const getHistoryEndpointTitle = (endpoint: string, method: HttpMethod) => {
	const endpointMatch = historyEndpointKeys.find((regex) => regex.test(endpoint));
	if (!endpointMatch) return fallback(endpoint, method);

	const methods = historyEndpointMap.get(endpointMatch);
	if (!methods) return fallback(endpoint, method);

	const title = methods[method];
	if (!title) return fallback(endpoint, method);

	return title;
};

export const getHistoryEndpointBadge = (endpoint: string) => {
	const endpointMatch = endpointBadgeMapKeys.find((regex) => regex.test(endpoint));

	if (!endpointMatch) return undefined;

	const match = endpointBadgeMap.get(endpointMatch);

	return match;
};
