import { toast } from '@pocitarna-nx-2023/ui';
import { type ApiBody } from '@pocitarna-nx-2023/zodios';
import Link from 'next/link';

export const onInventoryItemSuccess = (inventoryItem: ApiBody<'getInventoryItems'>[number]) => {
	const message = (
		<>
			Aktualizováno -{' '}
			<Link
				href={`/inventory/${inventoryItem.inventoryId}?selectedItem=${inventoryItem.productEnvelopeId}|${inventoryItem.productId}`}
				className="text-link"
				target="_blank"
			>
				{inventoryItem.code}
			</Link>
		</>
	);

	toast.success(message);
};
