import { CustomerClaimStatusMessage } from '@pocitarna-nx-2023/config';
import { Badge, Container, SidebarGrid, Stack, Title } from '@pocitarna-nx-2023/ui';
import { formatCustomerClaimCode } from '@pocitarna-nx-2023/utils';
import { apiHooks } from '@pocitarna-nx-2023/zodios-client';
import { type GetServerSideProps } from 'next';
import { type FC, Fragment } from 'react';
import { CustomerClaimFlowHandler } from '../../components/customerClaim/CustomerClaimFlowHandler';
import { CustomerClaimHistory } from '../../components/history/customerClaim/CustomerClaimHistory';
import { HistoryAccordion } from '../../components/history/HistoryAccordion';
import { NotesManagement } from '../../components/NotesManagement';
import { ProductPlacementInfo } from '../../components/product/ProductPlacementInfo';
import { ProductPriceOverview } from '../../components/product/ProductPriceOverview';
import { ProductDefectCard } from '../../components/productDefect/ProductDefectCard';
import { useCustomerClaimData } from '../../hooks/useCustomerClaimData';
import { useUserHasScope } from '../../hooks/useUserHasScope';
import { checkScope } from '../../utils/checkScope';

// FIXME: this whole page relies on the fact that there is ONE product associated to the service case (especially components: external ServiceCaseTransitionHandler, ServiceCasePrints, ServiceCaseStatusButtons).
// For now I'm just showing multiple product-info cards if there are multiple products, but should the whole logic be altered to accommodate the order service-case logic, since probably this flow will change in the future?

type Props = { customerClaimId: string };

const CustomerClaim: FC<Props> = ({ customerClaimId }) => {
	const { data: customerClaimData, invalidate } = apiHooks.useGetCustomerClaim(
		{ params: { customerClaimId } },
		{ enabled: Boolean(customerClaimId) },
	);
	const customerClaim = customerClaimData?._data;
	const { product, productDefects, allAffectedProducts } = useCustomerClaimData(customerClaimId, 'all-affected-products');

	const hasCustomerClaimWriteRights = useUserHasScope('customerClaimWrite');

	if (!customerClaim || !product) return null;

	return (
		<Container>
			<Stack>
				<Title
					title={`Zákaznická reklamace ${formatCustomerClaimCode(customerClaim.code)}`}
					backlink={{ url: '/customer-claim', label: ' Zákaznické reklamace' }}
					titleChildren={<Badge variant="info">{CustomerClaimStatusMessage[customerClaim.status]}</Badge>}
				/>
				<SidebarGrid>
					<Stack>
						{productDefects.map((defect) => (
							<ProductDefectCard defect={defect} product={product} key={defect.id} />
						))}

						<HistoryAccordion defaultOpen>
							<CustomerClaimHistory customerClaim={customerClaim}>
								{customerClaim?.status === 'NEW' ? (
									<CustomerClaimFlowHandler
										productId={product.id}
										productDefects={productDefects}
										customerClaim={customerClaim}
										disabled={!hasCustomerClaimWriteRights}
									/>
								) : null}
							</CustomerClaimHistory>
						</HistoryAccordion>
					</Stack>
					<Stack>
						{allAffectedProducts.map((product) => (
							<Fragment key={product.id}>
								<ProductPlacementInfo product={product} />
								<ProductPriceOverview product={product} />
							</Fragment>
						))}
						<NotesManagement
							entity={customerClaim}
							entityType="customerClaim"
							invalidate={invalidate}
							disabled={!hasCustomerClaimWriteRights}
						/>
					</Stack>
				</SidebarGrid>
			</Stack>
		</Container>
	);
};

export default CustomerClaim;

export const getServerSideProps: GetServerSideProps = async (ctx) => {
	const customerClaimId = ctx.params?.customerClaimId;

	return {
		props: {
			customerClaimId:
				typeof customerClaimId === 'string' ? customerClaimId : Array.isArray(customerClaimId) ? customerClaimId[0] : '',
		},
		redirect: await checkScope(ctx, 'customerClaimRead'),
	};
};
