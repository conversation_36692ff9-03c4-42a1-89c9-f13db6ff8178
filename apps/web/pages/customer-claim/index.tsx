import { CUSTOMER_CLAIM_STATUS_NAMES, CustomerClaimStatusMessage } from '@pocitarna-nx-2023/config';
import { Container, Stack, Title } from '@pocitarna-nx-2023/ui';
import { type GetServerSideProps, type NextPage } from 'next';
import { CustomerClaimListTable } from '../../components/customerClaim/CustomerClaimListTable';
import { FilteringTabs } from '../../components/filtering/FilteringTabs';
import { type FilterItem } from '../../components/filtering/Filters';
import { ServiceDefectsProvider } from '../../contexts/ServiceDefectsContext';
import { SortingProvider } from '../../contexts/SortingContext';
import { checkScope } from '../../utils/checkScope';

const filterDefinition: FilterItem[] = [
	{
		key: 'status',
		label: 'Stav',
		type: 'multi',
		items: [
			...CUSTOMER_CLAIM_STATUS_NAMES.map((status) => ({
				value: status,
				label: CustomerClaimStatusMessage[status],
			})),
		],
	},
];

const CustomerClaims: NextPage = () => {
	return (
		<SortingProvider>
			<ServiceDefectsProvider>
				<Container>
					<Stack>
						<Title title="Zákaznické reklamace" />

						<Stack gap={4}>
							<FilteringTabs
								tabs={[
									{ key: 'NEW', label: CustomerClaimStatusMessage['NEW'], filter: { status: { eq: 'NEW' } } },
									{
										key: 'CLOSED',
										label: 'Archiv',
										filter: { status: { eq: 'CLOSED' } },
									},
								]}
								filterEndpoint="getCustomerClaims"
								filterDefinition={filterDefinition}
							>
								{(tabFilter, filters) => <CustomerClaimListTable tabFilter={tabFilter} filters={filters} />}
							</FilteringTabs>
						</Stack>
					</Stack>
				</Container>
			</ServiceDefectsProvider>
		</SortingProvider>
	);
};

export default CustomerClaims;

export const getServerSideProps: GetServerSideProps = async (ctx) => {
	return {
		props: {},
		redirect: await checkScope(ctx, 'customerClaimRead'),
	};
};
