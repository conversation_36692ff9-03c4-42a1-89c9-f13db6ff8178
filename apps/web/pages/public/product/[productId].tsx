import { MAX_POSITIVE_INTEGER } from '@pocitarna-nx-2023/config';
import { Page, PageTitle, Stack, Title } from '@pocitarna-nx-2023/ui';
import { formatProductCode } from '@pocitarna-nx-2023/utils';
import { apiHooks } from '@pocitarna-nx-2023/zodios-client';
import { type GetServerSideProps } from 'next';
import { type ReactElement, useMemo } from 'react';
import { CosmeticDefectCard } from '../../../components/cosmeticDefects/CosmeticDefectCard';
import { PublicProductFilesCard } from '../../../components/public/product/PublicProductFilesCard';
import { type NextPageWithLayout } from '../../../types/next';

type Props = { productId: string };

const PublicProductDetail: NextPageWithLayout<Props> = ({ productId }) => {
	const { data } = apiHooks.useGetPublicProduct({ params: { productId } });
	const { data: batchData } = apiHooks.useGetBatch(
		{ params: { batchId: data?._data.batchId ?? '' } },
		{ enabled: Boolean(data?._data.batchId) },
	);

	const { data: productCosmeticDefectsData } = apiHooks.useGetPublicProductCosmeticDefects({
		params: { productId },
		queries: {
			page: 1,
			limit: MAX_POSITIVE_INTEGER,
			sort: ['cosmeticDefect.name'],
		},
	});
	const productCosmeticDefects = useMemo(() => productCosmeticDefectsData?._data ?? [], [productCosmeticDefectsData?._data]);

	const product = data?._data;
	const batch = batchData?._data;

	if (!product || !batch) return null;

	return (
		<>
			<PageTitle title={`Produkt ${formatProductCode(product.code)}`} />

			<Page className="min-h-full p-4 justify-center flex flex-col md:p-10">
				<Stack>
					<Title title={`Produkt ${formatProductCode(product.code)}`} />
					<Stack>
						<PublicProductFilesCard product={product} />
					</Stack>
					<Stack>
						<h2>Kosmetické vady</h2>
						{productCosmeticDefects.map((cosmeticDefect) => (
							<CosmeticDefectCard productCosmeticDefect={cosmeticDefect} key={cosmeticDefect.id} variant="public" disabled />
						))}
					</Stack>
				</Stack>
			</Page>
		</>
	);
};

export default PublicProductDetail;

PublicProductDetail.getLayout = (page: ReactElement) => {
	return page;
};

export const getServerSideProps: GetServerSideProps = async (ctx) => {
	const productId = ctx.params?.productId;

	return {
		props: {
			productId: typeof productId === 'string' ? productId : Array.isArray(productId) ? productId[0] : '',
		},
	};
};
