import { MAX_POSITIVE_INTEGER } from '@pocitarna-nx-2023/config';
import { Alert } from '@pocitarna-nx-2023/ui';
import { formatBatchCode, uniques } from '@pocitarna-nx-2023/utils';
import { apiHooks } from '@pocitarna-nx-2023/zodios-client';
import Link from 'next/link';
import {
	createContext,
	type FC,
	Fragment,
	type PropsWithChildren,
	type ReactNode,
	useCallback,
	useContext,
	useMemo,
	useState,
} from 'react';

type BatchDetailPageDataContext = {
	updateProductList: (productId: string, add: boolean) => void;
	hasTemporaryAttributeValues: boolean;
	updateEnvelopeCreationReadiness: (status: boolean) => void;
	isReadyForEnvelopeCreation: boolean;
	productsWithSnDuplicates: string[];
	snDuplicatesMessage: ReactNode;
};

const BatchDetailPageDataContext = createContext<BatchDetailPageDataContext>({
	updateProductList: () => void 0,
	hasTemporaryAttributeValues: false,
	updateEnvelopeCreationReadiness: (_status: boolean) => void 0,
	isReadyForEnvelopeCreation: false,
	productsWithSnDuplicates: [],
	snDuplicatesMessage: null,
});

type Props = {
	batchId: string;
};

export const BatchDetailPageDataProvider: FC<PropsWithChildren<Props>> = ({ batchId, children }) => {
	const [temporaryAttributeValuesArray, setTemporaryAttributeValuesArray] = useState<string[]>([]);
	const [envelopeCreationReadinessArray, setEnvelopeCreationReadinessArray] = useState<boolean[]>([]);

	const updateProductList = useCallback((productId: string, add: boolean) => {
		setTemporaryAttributeValuesArray((prevArray) => {
			if (add) {
				return prevArray.includes(productId) ? prevArray : [...prevArray, productId];
			}
			return prevArray.filter((item) => item !== productId);
		});
	}, []);

	const updateEnvelopeCreationReadiness = useCallback((status: boolean) => {
		setEnvelopeCreationReadinessArray((prevState) => [...prevState, status]);
	}, []);

	const { data: batchSnDuplicatesData } = apiHooks.useGetBatchSnDuplicates({
		params: { batchId },
	});

	const batchSnDuplicates = useMemo(() => batchSnDuplicatesData?._data ?? [], [batchSnDuplicatesData?._data]);
	const batchWithDuplicatesIds = useMemo(() => uniques(batchSnDuplicates.map((entry) => entry.duplicateBatchId)), [batchSnDuplicates]);

	const { data: batchesWithDuplicateProductsData } = apiHooks.useGetBatches(
		{
			queries: {
				page: 1,
				limit: MAX_POSITIVE_INTEGER,
				filter: {
					id: { eq: batchWithDuplicatesIds },
				},
			},
		},
		{ enabled: batchWithDuplicatesIds.length > 0 },
	);

	const batchesWithDuplicateProducts = useMemo(
		() => batchesWithDuplicateProductsData?._data ?? [],
		[batchesWithDuplicateProductsData?._data],
	);

	const snDuplicatesMessage = useMemo(() => {
		if (batchesWithDuplicateProducts.length === 0) return null;

		return (
			<Alert variant="destructive">
				Import obsahoval produkty s SN, které již byly dříve importovány ve várce č.{' '}
				{batchesWithDuplicateProducts.map((batch, idx) => (
					<Fragment key={batch.id}>
						<Link href={`/batch/${batch.id}`} className="text-link">
							{formatBatchCode(batch.code)}
						</Link>
						{idx < batchesWithDuplicateProducts.length - 1 ? ', ' : ''}
					</Fragment>
				))}
			</Alert>
		);
	}, [batchesWithDuplicateProducts]);

	return (
		<BatchDetailPageDataContext.Provider
			value={{
				updateProductList,
				hasTemporaryAttributeValues: temporaryAttributeValuesArray.length > 0,
				updateEnvelopeCreationReadiness,
				isReadyForEnvelopeCreation: envelopeCreationReadinessArray.every((entry) => entry === true),
				productsWithSnDuplicates: batchSnDuplicates.map((entry) => entry.productId),
				snDuplicatesMessage,
			}}
		>
			{children}
		</BatchDetailPageDataContext.Provider>
	);
};

export const useBatchDetailPageData = () => useContext(BatchDetailPageDataContext);
