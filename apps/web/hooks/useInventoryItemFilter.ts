import { type FilterRules } from '@pocitarna-nx-2023/ui';
import { type Filtering } from '@pocitarna-nx-2023/zodios';

export const useInventoryItemFilter = (filters: FilterRules): Filtering => {
	const filter = {
		...(Array.isArray(filters['inventoryStatus']) && filters['inventoryStatus'].length > 0
			? { inventoryStatus: { eq: filters['inventoryStatus'] } }
			: {}),
		...(filters['code'] && typeof filters['code'] === 'string' ? getCodeFilter(filters['code']) : {}),
		...(filters['productCategoryId'] ? { productCategoryId: { eq: filters['productCategoryId'] } } : {}),
		...(filters['manufacturer'] ? { 'attributeValue.value->>0': { eq: `%${filters['manufacturer']}%` } } : {}),
	};

	return filter;
};

const getCodeFilter = (code: string): Filtering => {
	if (code.startsWith('PCN')) {
		return { code: { eq: `%${code}%` } };
	} else {
		return { productEnvelopeCode: { eq: `%${code}%` } };
	}
};
