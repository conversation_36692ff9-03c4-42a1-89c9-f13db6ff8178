import { MAX_POSITIVE_INTEGER } from '@pocitarna-nx-2023/config';
import { type ApiBody } from '@pocitarna-nx-2023/zodios';
import { apiHooks } from '@pocitarna-nx-2023/zodios-client';
import { useMemo } from 'react';
import { z } from 'zod';
import { calculateProductGrade } from '../utils/productGrading';

export const useCosmeticDefectsFormData = (product: ApiBody<'getProduct'>) => {
	const { data: cosmeticAreasData } = apiHooks.useGetCosmeticAreas({
		queries: {
			page: 1,
			limit: MAX_POSITIVE_INTEGER,
			filter: { productCategoryId: { eq: product.productCategoryId } },
			sort: ['name'],
		},
	});
	const cosmeticAreas = useMemo(() => cosmeticAreasData?._data ?? [], [cosmeticAreasData?._data]);

	const { data: categoryCosmeticDefectsData } = apiHooks.useGetCosmeticDefects({
		queries: {
			page: 1,
			limit: MAX_POSITIVE_INTEGER,
			filter: { 'productCategoryCosmeticDefects.productCategoryId': { eq: product.productCategoryId } },

			sort: ['name'],
		},
	});
	const categoryCosmeticDefects = useMemo(() => categoryCosmeticDefectsData?._data ?? [], [categoryCosmeticDefectsData?._data]);

	const cosmeticDefectShape = cosmeticAreas.reduce(
		(shape, area) => {
			shape[area.id] = z.array(z.string()).nullable().optional();
			return shape;
		},
		{} as Record<string, z.ZodTypeAny>,
	);

	const schema = z.object({
		cosmeticDefects: z.object(cosmeticDefectShape),
		gradeId: z.string().uuid(),
	});

	const { data: productCosmeticDefectsData } = apiHooks.useGetProductCosmeticDefects({
		queries: {
			page: 1,
			limit: MAX_POSITIVE_INTEGER,
			filter: { productId: { eq: product.id } },
			sort: ['cosmeticDefect.name'],
		},
	});
	const productCosmeticDefects = useMemo(() => productCosmeticDefectsData?._data ?? [], [productCosmeticDefectsData?._data]);

	const { data: rankedGradesData, isFetched } = apiHooks.useGetRankedGrades({});

	const rankedGrades = useMemo(() => rankedGradesData?._data ?? [], [rankedGradesData?._data]);

	const calculatedGradeId = useMemo(() => {
		if (!isFetched) return null;
		return calculateProductGrade({ defects: productCosmeticDefects, rankedGrades, product });
	}, [productCosmeticDefects, rankedGrades, isFetched, product]);

	const defaultValues = useMemo(() => {
		return {
			cosmeticDefects: cosmeticAreas.reduce(
				(values, area) => {
					const matches = productCosmeticDefects.filter((defect) => defect.cosmeticAreaId === area.id);

					values[area.id] = matches.map((item) => item.cosmeticDefect.id);
					return values;
				},
				{} as Record<string, string[]>,
			),
			gradeId: calculatedGradeId ?? '',
		};
	}, [cosmeticAreas, productCosmeticDefects, calculatedGradeId]);

	const allRelevantDefectsHaveImage = useMemo(
		() =>
			productCosmeticDefects.every((defect) => {
				if (!defect.cosmeticDefect?.pictureRequired) return true;
				return defect.files.length > 0;
			}),
		[productCosmeticDefects],
	);

	return {
		cosmeticAreas,
		categoryCosmeticDefects,
		productCosmeticDefects,
		rankedGrades,
		calculatedGradeId,
		defaultValues,
		schema,
		allRelevantDefectsHaveImage,
	};
};
