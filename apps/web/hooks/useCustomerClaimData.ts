import { MAX_POSITIVE_INTEGER } from '@pocitarna-nx-2023/config';
import { filterUndefined, uniques } from '@pocitarna-nx-2023/utils';
import { apiHooks } from '@pocitarna-nx-2023/zodios-client';

export const useCustomerClaimData = (customerClaimId: string, scope?: 'all-affected-products') => {
	const { data: productDefectsData } = apiHooks.useGetAllProductDefects({
		queries: {
			sort: [['createdAt', 'asc']],
			page: 1,
			limit: MAX_POSITIVE_INTEGER,
			filter: {
				customerClaimId: {
					eq: customerClaimId,
				},
			},
		},
	});
	const productDefects = productDefectsData?._data ?? [];
	const referenceDefect = productDefects[0];

	const { data: productData } = apiHooks.useGetProduct(
		{ params: { productId: referenceDefect?.productId ?? '' } },
		{ enabled: Boolean(referenceDefect?.productId) },
	);
	const product = productData?._data;

	const { data: allAffectedProductsData } = apiHooks.useGetAllProducts(
		{
			queries: {
				page: 1,
				limit: MAX_POSITIVE_INTEGER,
				filter: { id: { eq: uniques(filterUndefined(productDefects.map((item) => item?.productId))) } },
			},
		},
		{ enabled: scope === 'all-affected-products' && productDefects.length > 0 },
	);

	const allAffectedProducts = allAffectedProductsData?._data ?? [];

	return { product, referenceDefect, productDefects, allAffectedProducts };
};
