import { ProductCosmeticDefectController } from '@pocitarna-nx-2023/database';
import { productCosmeticDefectApi } from '@pocitarna-nx-2023/zodios';
import type { zodiosContext } from '@zodios/express';
import { scopeMiddleware } from '../middlewares/scope';
import { getListProps } from '../utils/getListProps';
import { respond, respondWithPaging } from '../utils/respond';

export const productCosmeticDefectRouter = (ctx: ReturnType<typeof zodiosContext>) => {
	const router = ctx.router(productCosmeticDefectApi);

	router.get('/', scopeMiddleware('productTest'), async () => {
		const [data, getCount] = await new ProductCosmeticDefectController().list(getListProps());
		respondWithPaging<'getProductCosmeticDefects'>(data, await getCount());
	});

	router.post('/product/:productId/cosmeticArea/:cosmeticAreaId', scopeMiddleware('productTest'), async (req) => {
		const { cosmeticDefectIds } = req.body;

		const presentDefects = await new ProductCosmeticDefectController().findByProduct(req.params.productId, {
			filter: { cosmeticAreaId: { eq: req.params.cosmeticAreaId } },
		});
		const presentDefectsIds = presentDefects.map((defect) => defect.cosmeticDefectId);
		const toAdd = cosmeticDefectIds.filter((id) => !presentDefectsIds.includes(id));
		const toRemove = presentDefectsIds.filter((id) => !cosmeticDefectIds.includes(id));

		if (toRemove.length > 0) {
			await new ProductCosmeticDefectController().unlink(req.params.productId, toRemove);
		}

		if (toAdd.length > 0) {
			await new ProductCosmeticDefectController().link({
				productId: req.params.productId,
				cosmeticAreaId: req.params.cosmeticAreaId,
				cosmeticDefectIds: toAdd,
				isFix: req.body.isFix,
			});
		}

		return respond<'assignCosmeticDefectsToProductArea'>(true);
	});

	router.patch('/:productCosmeticDefectId/files', scopeMiddleware('admin'), async (req) => {
		const fileIds = req.body;
		await new ProductCosmeticDefectController().addFiles(req.params.productCosmeticDefectId, fileIds);

		respond<'addFilesToProductCosmeticDefect'>(true);
	});

	router.delete(
		'/:productCosmeticDefectId/files/:fileId',
		scopeMiddleware('serviceWrite', 'warrantyClaimWrite', 'productWrite', 'productTest'),
		async (req, res) => {
			const entity = await new ProductCosmeticDefectController().deleteFile(req.params.productCosmeticDefectId, req.params.fileId);
			if (!entity) return res.status(500).send();

			respond<'deleteProductCosmeticDefectFile'>(true);
		},
	);

	return router;
};
