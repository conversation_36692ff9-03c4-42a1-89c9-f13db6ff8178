import { BatchDefectController } from '@pocitarna-nx-2023/database';
import { batchDefectApi } from '@pocitarna-nx-2023/zodios';
import type { zodiosContext } from '@zodios/express';
import { scopeMiddleware } from '../middlewares/scope';
import { respond } from '../utils/respond';

export const batchDefectRouter = (ctx: ReturnType<typeof zodiosContext>) => {
	const router = ctx.router(batchDefectApi);

	router.delete('/defect/:defectId/files/:fileId', scopeMiddleware('batchDelivery'), async (req, res) => {
		const entity = await new BatchDefectController().deleteFile(req.params.defectId, req.params.fileId);
		if (!entity) return res.status(500).send();

		return respond<'deleteBatchDefectFile'>(true);
	});

	return router;
};
