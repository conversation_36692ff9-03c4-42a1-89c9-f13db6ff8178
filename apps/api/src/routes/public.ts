import { publicApi } from '@pocitarna-nx-2023/zodios';
import type { zodiosContext } from '@zodios/express';
import { publicProductRouter } from './public/product';

export const publicRouter = (ctx: ReturnType<typeof zodiosContext>) => {
	const router = ctx.router(publicApi);

	router.use('/product', publicProductRouter(ctx));

	router.use('/customer-claim', publicProductRouter(ctx));

	return router;
};
