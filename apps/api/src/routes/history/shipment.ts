import { ShipmentHistoryController } from '@pocitarna-nx-2023/database';
import { shipmentHistoryApi } from '@pocitarna-nx-2023/zodios';
import type { zodiosContext } from '@zodios/express';
import { scopeMiddleware } from '../../middlewares/scope';
import { mergeHistoriesByAction } from '../../utils/mergeHistoriesByAction';
import { respond } from '../../utils/respond';

export const shipmentHistoryRouter = (ctx: ReturnType<typeof zodiosContext>) => {
	const router = ctx.router(shipmentHistoryApi);

	router.get('/:shipmentId', scopeMiddleware('home'), async (req) => {
		const [shipment] = await Promise.all([new ShipmentHistoryController().listById(req.params.shipmentId)]);

		const mergedHistories = mergeHistoriesByAction({ shipment });

		respond<'getShipmentHistory'>(mergedHistories);
	});

	return router;
};
