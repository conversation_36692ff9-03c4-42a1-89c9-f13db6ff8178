import {
	FileServiceCaseH<PERSON>ory<PERSON>ontroller,
	ProductDefectHistoryController,
	ServiceCaseHistoryController,
} from '@pocitarna-nx-2023/database';
import { serviceCaseHistoryApi } from '@pocitarna-nx-2023/zodios';
import type { zodiosContext } from '@zodios/express';
import { scopeMiddleware } from '../../middlewares/scope';
import { mergeHistoriesByAction } from '../../utils/mergeHistoriesByAction';
import { respond } from '../../utils/respond';

export const serviceCaseHistoryRouter = (ctx: ReturnType<typeof zodiosContext>) => {
	const router = ctx.router(serviceCaseHistoryApi);

	router.get('/:serviceCaseId', scopeMiddleware('serviceRead'), async (req) => {
		const [serviceCase, fileServiceCase, productDefect] = await Promise.all([
			new ServiceCaseHistoryController().listById(req.params.serviceCaseId),
			new FileServiceCaseHistoryController().listByServiceCaseId(req.params.serviceCaseId),
			new ProductDefectHistoryController().listByServiceCaseId(req.params.serviceCaseId),
		]);

		const mergedHistories = mergeHistoriesByAction({ serviceCase, fileServiceCase, productDefect });

		respond<'getServiceCaseHistory'>(mergedHistories);
	});

	return router;
};
