import { BatchHistoryController, FileBatchHistoryController } from '@pocitarna-nx-2023/database';
import { batchHistoryApi } from '@pocitarna-nx-2023/zodios';
import type { zodiosContext } from '@zodios/express';
import { scopeMiddleware } from '../../middlewares/scope';
import { mergeHistoriesByAction } from '../../utils/mergeHistoriesByAction';
import { respond } from '../../utils/respond';

export const batchHistoryRouter = (ctx: ReturnType<typeof zodiosContext>) => {
	const router = ctx.router(batchHistoryApi);

	router.get('/:batchId', scopeMiddleware('batchRead'), async (req) => {
		const [batch, fileBatch] = await Promise.all([
			new BatchHistoryController().listById(req.params.batchId),
			new FileBatchHistoryController().listByBatchId(req.params.batchId),
		]);

		const mergedHistories = mergeHistoriesByAction({ batch, fileBatch });

		respond<'getBatchHistory'>(mergedHistories);
	});

	return router;
};
