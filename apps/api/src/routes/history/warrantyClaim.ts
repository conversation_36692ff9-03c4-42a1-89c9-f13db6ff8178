import {
	FileWarrantyClaimH<PERSON>ory<PERSON>ontroller,
	ProductDefectHistoryController,
	WarrantyClaimHistoryController,
} from '@pocitarna-nx-2023/database';
import { warrantyClaimHistoryApi } from '@pocitarna-nx-2023/zodios';
import type { zodiosContext } from '@zodios/express';
import { scopeMiddleware } from '../../middlewares/scope';
import { mergeHistoriesByAction } from '../../utils/mergeHistoriesByAction';
import { respond } from '../../utils/respond';

export const warrantyClaimHistoryRouter = (ctx: ReturnType<typeof zodiosContext>) => {
	const router = ctx.router(warrantyClaimHistoryApi);

	router.get('/:warrantyClaimId', scopeMiddleware('warrantyClaimRead'), async (req) => {
		const [warrantyClaim, fileWarrantyClaim, productDefect] = await Promise.all([
			new WarrantyClaimHistoryController().listById(req.params.warrantyClaimId),
			new FileWarrantyClaimHistoryController().listByWarrantyClaimId(req.params.warrantyClaimId),
			new ProductDefectHistoryController().listByWarrantyClaimId(req.params.warrantyClaimId),
		]);

		const mergedHistories = mergeHistoriesByAction({ warrantyClaim, fileWarrantyClaim, productDefect });

		respond<'getWarrantyClaimHistory'>(mergedHistories);
	});

	return router;
};
