import {
	FileProductHistory<PERSON>ontroller,
	ProductAttributeValueHistoryController,
	ProductCosmeticDefectHistoryController,
	ProductHistoryController,
} from '@pocitarna-nx-2023/database';
import { productHistoryApi } from '@pocitarna-nx-2023/zodios';
import type { zodiosContext } from '@zodios/express';
import { scopeMiddleware } from '../../middlewares/scope';
import { mergeHistoriesByAction } from '../../utils/mergeHistoriesByAction';
import { respond } from '../../utils/respond';

export const productHistoryRouter = (ctx: ReturnType<typeof zodiosContext>) => {
	const router = ctx.router(productHistoryApi);

	router.get('/:productId', scopeMiddleware('productRead'), async (req) => {
		const [product, productAttributeValue, fileProduct, productCosmeticDefect] = await Promise.all([
			new ProductHistoryController().listById(req.params.productId),
			new ProductAttributeValueHistoryController().listByProductId(req.params.productId),
			new FileProductHistoryController().listByProductId(req.params.productId),
			new ProductCosmeticDefectHistoryController().listByProductId(req.params.productId),
		]);

		const mergedHistories = mergeHistoriesByAction({
			product,
			productAttributeValue,
			fileProduct,
			productCosmeticDefect,
		});

		respond<'getProductHistory'>(mergedHistories);
	});

	return router;
};
