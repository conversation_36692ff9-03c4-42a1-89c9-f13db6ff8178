import { CustomerClaimCodeController, CustomerClaimController } from '@pocitarna-nx-2023/database';
import { customerClaimApi } from '@pocitarna-nx-2023/zodios';
import type { zodiosContext } from '@zodios/express';
import { scopeMiddleware } from '../middlewares/scope';
import { getListProps } from '../utils/getListProps';
import { respond, respondWithPaging } from '../utils/respond';

export const customerClaimRouter = (ctx: ReturnType<typeof zodiosContext>) => {
	const router = ctx.router(customerClaimApi);

	router.post('/', scopeMiddleware('customerClaimWrite'), async (req) => {
		const customerClaim = await new CustomerClaimController().create(req.body);
		respond<'createCustomerClaim'>(customerClaim);
	});

	router.get('/', scopeMiddleware('customerClaimRead'), async () => {
		const [data, getCount] = await new CustomerClaimController().list(getListProps());

		return respondWithPaging<'getCustomerClaims'>(data, await getCount());
	});

	router.get('/code/:customerClaimCodeId', scopeMiddleware('customerClaimRead'), async (req, res) => {
		const customerClaimCode = await new CustomerClaimCodeController().findById(req.params.customerClaimCodeId);
		if (!customerClaimCode) return res.status(404).json();
		respond<'getCustomerClaimCode'>(customerClaimCode);
	});

	router.get('/:customerClaimId', scopeMiddleware('customerClaimRead'), async (req, res) => {
		const customerClaim = await new CustomerClaimController().findById(req.params.customerClaimId);
		if (!customerClaim) return res.status(404).json();
		respond<'getCustomerClaim'>(customerClaim);
	});

	router.patch('/:customerClaimId', scopeMiddleware('customerClaimWrite'), async (req) => {
		await new CustomerClaimController().update(req.params.customerClaimId, req.body);
		respond<'updateCustomerClaim'>(true);
	});

	return router;
};
