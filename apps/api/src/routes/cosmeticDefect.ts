import { CosmeticDefectController } from '@pocitarna-nx-2023/database';
import { cosmeticDefectApi } from '@pocitarna-nx-2023/zodios';
import type { zodiosContext } from '@zodios/express';
import { scopeMiddleware } from '../middlewares/scope';
import { getListProps } from '../utils/getListProps';
import { respond, respondWithPaging } from '../utils/respond';

export const cosmeticDefectRouter = (ctx: ReturnType<typeof zodiosContext>) => {
	const router = ctx.router(cosmeticDefectApi);

	router.post('/', scopeMiddleware('admin'), async (req, res) => {
		const cosmeticDefect = await new CosmeticDefectController().create(req.body);
		if (!cosmeticDefect) return res.status(500).json();

		respond<'createCosmeticDefect'>(true);
	});

	router.get('/', scopeMiddleware('admin', 'batchRead', 'batchWrite'), async () => {
		const [cosmeticDefects, getCount] = await new CosmeticDefectController().list(getListProps());
		respondWithPaging<'getCosmeticDefects'>(cosmeticDefects, await getCount());
	});

	router.get('/:cosmeticDefectId', scopeMiddleware('home'), async (req, res) => {
		const cosmeticDefect = await new CosmeticDefectController().findById(req.params.cosmeticDefectId);
		if (!cosmeticDefect) return res.status(404).json();

		respond<'getCosmeticDefect'>(cosmeticDefect);
	});

	router.patch('/:cosmeticDefectId', scopeMiddleware('admin'), async (req) => {
		await new CosmeticDefectController().update(req.params.cosmeticDefectId, {
			...req.body,
			productCategories: req.body.productCategories ?? [],
			cosmeticAreas: req.body.cosmeticAreas ?? [],
		});

		respond<'updateCosmeticDefect'>(true);
	});

	router.delete('/:cosmeticDefectId', scopeMiddleware('admin'), async (req, res) => {
		const cosmeticDefect = await new CosmeticDefectController().findById(req.params.cosmeticDefectId);
		if (!cosmeticDefect) return res.status(404).send();

		const result = await new CosmeticDefectController().delete(cosmeticDefect.id);

		respond<'deleteCosmeticDefect'>(result);
	});

	return router;
};
