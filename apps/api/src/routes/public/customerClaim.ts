import { list<PERSON><PERSON>, ProductController, ProductCosmeticDefectController } from '@pocitarna-nx-2023/database';
import { publicCustomerClaimApi } from '@pocitarna-nx-2023/zodios';
import type { zodiosContext } from '@zodios/express';
import { getListProps } from '../../utils/getListProps';
import { respond } from '../../utils/respond';

export const publicCustomerClaimRouter = (ctx: ReturnType<typeof zodiosContext>) => {
	const router = ctx.router(publicCustomerClaimApi);

	router.get('/:productId', async (req, res) => {
		const product = await new ProductController().findById(req.params.productId);
		if (!product) return res.status(404).json();

		respond<'getPublicProduct'>(product);
	});

	router.get('/:productId/cosmetic-defects', async (req) => {
		const productCosmeticDefects = await new ProductCosmeticDefectController().findByProduct(req.params.productId, listAll());

		respond<'getPublicProductCosmeticDefects'>(productCosmeticDefects);
	});

	router.get('/:productId/files', async (req) => {
		const [files] = await new ProductController().listFiles(req.params.productId, getListProps({ type: { eq: 'regular' } }));
		respond<'getPublicProductFiles'>(files);
	});

	return router;
};
