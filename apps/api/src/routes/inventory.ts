import {
	InventoryController,
	type InventoryItem,
	InventoryItemController,
	listAll,
	ProductController,
	useAuthentication,
} from '@pocitarna-nx-2023/database';
import { formatProductCode, stripCode } from '@pocitarna-nx-2023/utils';
import { inventoryApi } from '@pocitarna-nx-2023/zodios';
import type { zodiosContext } from '@zodios/express';
import { type Repository } from 'typeorm';
import { scopeMiddleware } from '../middlewares/scope';
import { getListProps } from '../utils/getListProps';
import { isFilteringBy } from '../utils/isFilteringBy';
import { respond, respondWithPaging } from '../utils/respond';

export const inventoryRouter = (ctx: ReturnType<typeof zodiosContext>) => {
	const router = ctx.router(inventoryApi);

	router.post('/', scopeMiddleware('warehouseManage'), async () => {
		const inventory = await new InventoryController().create({});

		const [productsToProcess] = await new ProductController().list(
			listAll({
				filter: {
					status: {
						eq: [
							'AT_SUPPLIER',
							'ON_THE_WAY',
							'EXTERNAL_STOCK',
							'TO_CHECK',
							'TO_CHECK_SN',
							'TO_TEST',
							'TESTED',
							'SERVICE',
							'WARRANTY_CLAIM',
							'CUSTOMER_CLAIM',
							'STOCK',
							'FOR_SALE',
							'RESERVED',
							'AUTOPSY',
							'DEAD',
						],
					},
				},
			}),
		);

		const inventoryItemsData = await Promise.all(
			productsToProcess.map(async (product) => {
				const relatedData = await new InventoryController().getInventoryItemRelatedData({ product });

				const data: Parameters<Repository<InventoryItem>['create']>[0] = {
					inventory: { id: inventory.id },
					code: formatProductCode(product.code),
					...relatedData,
				};
				return data;
			}),
		);

		const inventoryItems = await new InventoryItemController().bulkCreate(inventoryItemsData);

		await new InventoryController().prepareInventoryPrices(inventory.id, inventoryItems);

		respond<'createInventory'>(inventory);
	});

	router.get('/', scopeMiddleware('warehouseRead'), async () => {
		const [inventories, getCount] = await new InventoryController().list(getListProps());
		respondWithPaging<'getInventories'>(inventories, await getCount());
	});

	router.get('/:inventoryId', scopeMiddleware('warehouseRead'), async (req, res) => {
		const inventory = await new InventoryController().findById(req.params.inventoryId);
		if (!inventory) return res.status(404).json();
		respond<'getInventory'>(inventory);
	});

	router.post('/:inventoryId/scan', scopeMiddleware('warehouseWrite'), async (req) => {
		const inventoryItemsToProcess = await new InventoryItemController().findByCode(req.params.inventoryId, req.body);

		const inventoryItems = await Promise.all(
			req.body.map(async (pcn) => {
				const inventoryItem = inventoryItemsToProcess.find((item) => item.code === pcn);
				const { warehousePositionId } = req.query;

				if (inventoryItem) {
					if (inventoryItem.scannedAt != null) {
						return { pcn, status: 'ERROR' as const, error: 'Produkt již byl načten' };
					}

					if (warehousePositionId) {
						await new ProductController().setWarehousePosition(inventoryItem.productId, warehousePositionId);
					}

					await new InventoryItemController().update(inventoryItem, {
						scannedAt: new Date(),
						scannedBy: useAuthentication()?.user ?? null,
						inventoryStatus: inventoryItem.inventoryStatus === 'OK' ? 'ERROR' : 'OK',
						postScanWarehousePositionId: warehousePositionId,
					});

					return { pcn, status: 'OK' as const };
				}

				const code = stripCode(pcn);
				const product = await new ProductController().findByCode(code);

				if (!product) return { pcn, status: 'ERROR' as const, error: 'Produkt nebyl nalezen' };

				const relatedData = await new InventoryController().getInventoryItemRelatedData({
					product,
					inventoryStatus: 'NEW_ADDITION',
				});

				await new InventoryItemController().create({
					inventory: { id: req.params.inventoryId },
					code: pcn,
					scannedAt: new Date(),
					scannedBy: useAuthentication()?.user ?? null,
					...relatedData,
				});

				return { pcn, status: 'OK' as const };
			}),
		);

		respond<'scanInventoryItem'>(inventoryItems);
	});

	router.get('/:inventoryId/items', scopeMiddleware('warehouseRead'), async (req) => {
		const listProps = getListProps({ inventoryId: { eq: req.params.inventoryId } });
		const isFilteringByManufacturer = isFilteringBy(listProps, 'attributeValue.value->>0');

		if (isFilteringByManufacturer) {
			const [inventoryItems, getCount] = await new InventoryItemController().listWithManufacturer(listProps);
			return respondWithPaging<'getInventoryItems'>(inventoryItems, await getCount());
		}

		const [inventoryItems, getCount] = await new InventoryItemController().list(listProps);
		respondWithPaging<'getInventoryItems'>(inventoryItems, await getCount());
	});

	router.patch('/:inventoryId/files', scopeMiddleware('warehouseManage'), async (req) => {
		const fileIds = req.body;
		const inventoryId = req.params.inventoryId;
		await new InventoryController().addFiles(inventoryId, fileIds);
		respond<'addFilesToInventory'>(true);
	});

	router.delete('/:inventoryId/files/:fileId', scopeMiddleware('warehouseManage'), async (req, res) => {
		const entity = await new InventoryController().deleteFile(req.params.inventoryId, req.params.fileId);
		if (!entity) return res.status(500).send();

		return respond<'deleteInventoryFile'>(true);
	});

	router.patch('/:inventoryId/:inventoryItemId', scopeMiddleware('warehouseManage'), async (req) => {
		await new InventoryItemController().handleInventoryItemAction({
			...req.body,
			inventoryId: req.params.inventoryId,
			inventoryItemId: req.params.inventoryItemId,
		});

		respond<'handleInventoryItemAction'>(true);
	});

	router.patch('/:inventoryId', scopeMiddleware('warehouseManage'), async (req) => {
		await new InventoryController().update(req.params.inventoryId, req.body);
		respond<'updateInventory'>(true);
	});

	router.get('/:inventoryId/excel', scopeMiddleware('warehouseRead'), async (req, res) => {
		const { variant } = req.query;
		const file = await new InventoryController().prepareExcelReport(req.params.inventoryId, variant);
		res.setHeader('Content-Type', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
		res.setHeader('Content-Disposition', `attachment; filename=inventory-${new Date().toISOString()}-${variant}.xlsx`);
		res.send(file);
	});

	return router;
};
