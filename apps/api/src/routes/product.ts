import { triggerShoptetExport } from '@pocitarna-nx-2023/aws';
import {
	AttributeController,
	type AttributeV<PERSON>ue,
	AttributeValueController,
	type Bat<PERSON>,
	BatchController,
	ConditionalAttributeController,
	EcommerceOrderController,
	FileController,
	listAll,
	listOne,
	NotificationController,
	type Product,
	type ProductAttributeValue,
	ProductAttributeValueController,
	ProductCategoryController,
	ProductCodeController,
	ProductController,
	ProductCosmeticDefectController,
	type ProductEnvelope,
	ProductEnvelopeController,
	ProductPriceController,
	ProductTaskController,
	ProductTestController,
	ServiceCaseController,
	useAuthentication,
	WarrantyClaimController,
	WarrantyController,
} from '@pocitarna-nx-2023/database';
import { reader } from '@pocitarna-nx-2023/reader';
import { formatProductCode, getProductStatusesAbove, uniques, uniquesBy } from '@pocitarna-nx-2023/utils';
import { productApi } from '@pocitarna-nx-2023/zodios';
import type { zodiosContext } from '@zodios/express';
import { scopeMiddleware } from '../middlewares/scope';
import { getListProps } from '../utils/getListProps';
import { respond, respondWithPaging } from '../utils/respond';

export const productRouter = (ctx: ReturnType<typeof zodiosContext>) => {
	const router = ctx.router(productApi);

	router.post('/', scopeMiddleware('batchWrite'), async (req, res) => {
		const { product, productPrice, attributeValues } = req.body;

		const productEntity = await new ProductController().create(product);
		if (!productEntity) return res.status(500).send();

		await new ProductController().addBuyPrice(productEntity.id, productPrice ?? 0);

		const textValues = attributeValues.filter((item) => !!item.value && !item.attributeValueId);
		await Promise.all(
			textValues.map(async (item) => {
				if (!item.value) return;
				const attributeValue = await new AttributeController().createTextValue(item.attributeId, String(item.value));
				item.attributeValueId = attributeValue.id; // Intentional side effect
			}),
		);

		await new ProductController().setAttributeValue(
			productEntity.id,
			attributeValues.filter((value): value is { attributeValueId: string; attributeId: string } => !!value.attributeValueId),
			['import', 'resolved'],
		);

		if (product.batch?.id) {
			await new ProductCodeController().create({ batch: { id: product.batch.id } });
		}

		respond<'createProduct'>(true);
	});

	router.get(
		'/',
		scopeMiddleware(
			'warrantyClaimRead',
			'warrantyClaimWrite',
			'serviceRead',
			'serviceWrite',
			'productRead',
			'productWrite',
			'batchRead',
			'batchWrite',
			'batchCheck',
			'stock',
			'batchDelivery',
		),
		async () => {
			const [products, getCount] = await new ProductController().list(getListProps());
			respondWithPaging<'getAllProducts'>(products, await getCount());
		},
	);

	router.get('/tested', scopeMiddleware('productTestLead'), async (req) => {
		const { autofill, attributeValue } = req.query;
		if (!autofill && !attributeValue) {
			const [testedProducts, getCount] = await new ProductController().list(getListProps({ status: { eq: 'TESTED' } }));
			return respondWithPaging<'getTestedProducts'>(testedProducts, await getCount());
		}

		const productIdsByAutofill: string[] = [];
		const productIdsByAttributeValue: string[] = [];

		if (autofill) {
			const [productTests] = await new ProductTestController().listByAutofill();
			const productIds = productTests.map((productTest) => productTest.productId);
			productIdsByAutofill.push(...productIds);
		}

		if (attributeValue) {
			const [attributeValues] = await new AttributeValueController().listWithTestedProductsByAttributeValue(attributeValue);
			const productIds = attributeValues.reduce<string[]>((acc, item) => {
				const productIds = item.products.map(({ productId }) => productId);
				acc.push(...productIds);
				return acc;
			}, []);
			productIdsByAttributeValue.push(...productIds);
		}

		const productIds = uniques([...productIdsByAutofill, ...productIdsByAttributeValue]);
		if (productIds.length === 0) return respondWithPaging([], 0);

		const [testedProducts, getCount] = await new ProductController().list(
			getListProps({ status: { eq: 'TESTED' }, id: { eq: productIds } }),
		);

		respondWithPaging<'getTestedProducts'>(testedProducts, await getCount());
	});

	router.get('/price', scopeMiddleware('stock', 'productRead'), async () => {
		const [allProductPrices] = await new ProductPriceController().list(getListProps());
		respond<'getPricesOfProducts'>(allProductPrices);
	});

	router.get(
		'/:productId',
		scopeMiddleware(
			'warrantyClaimRead',
			'warrantyClaimWrite',
			'serviceRead',
			'serviceWrite',
			'productRead',
			'productWrite',
			'testRead',
			'productTest',
			'batchRead',
			'batchWrite',
			'batchCheck',
			'stock',
			'batchDelivery',
		),
		async (req, res) => {
			const product = await new ProductController().findById(req.params.productId);
			if (!product) return res.status(404).json();
			respond<'getProduct'>(product);
		},
	);

	router.get(
		'/:productId/prices',
		scopeMiddleware(
			'warrantyClaimRead',
			'warrantyClaimWrite',
			'serviceRead',
			'serviceWrite',
			'productRead',
			'productWrite',
			'batchRead',
			'batchWrite',
			'batchCheck',
			'stock',
			'batchDelivery',
		),
		async (req) => {
			const [allProductPrices] = await new ProductPriceController().list(
				listAll({
					filter: {
						'entity.productId': { eq: req.params.productId },
					},
				}),
			);

			respond<'getAllProductPrices'>(allProductPrices);
		},
	);

	router.get(
		'/:productId/warranties',
		scopeMiddleware(
			'warrantyClaimRead',
			'warrantyClaimWrite',
			'productRead',
			'productWrite',
			'batchRead',
			'batchWrite',
			'batchCheck',
			'stock',
			'batchDelivery',
		),
		async (req) => {
			const productWarranties = await new ProductController().getProductWarranties(req.params.productId);
			respond<'getProductWarranties'>(productWarranties);
		},
	);

	router.get(
		'/:productId/order',
		scopeMiddleware(
			'warrantyClaimRead',
			'warrantyClaimWrite',
			'productRead',
			'productWrite',
			'batchRead',
			'batchWrite',
			'batchCheck',
			'stock',
			'batchDelivery',
		),
		async (req) => {
			const order = await new EcommerceOrderController().findByProduct(req.params.productId);
			respond<'getProductOrder'>(order);
		},
	);

	router.post('/:productId/warranties', scopeMiddleware('admin'), async (req) => {
		const productWarranty = await new WarrantyController().create({ ...req.body, product: { id: req.params.productId } });
		respond<'createProductWarranty'>(productWarranty);
	});

	router.patch('/:productId/warranties/:warrantyId', scopeMiddleware('admin'), async (req, res) => {
		const updatedProductWarranty = await new WarrantyController().update(req.params.warrantyId, req.body);
		if (!updatedProductWarranty) return res.status(404).json();
		respond<'updateProductWarranty'>(updatedProductWarranty);
	});

	router.delete('/:productId/warranties/:warrantyId', scopeMiddleware('admin'), async (req) => {
		await new WarrantyController().delete(req.params.warrantyId);
		respond<'deleteProductWarranty'>(true);
	});

	router.patch('/bulk', scopeMiddleware('batchWrite'), async (req) => {
		await Promise.all(req.body.ids.map((id) => new ProductController().update(id, req.body.data)));
		return respond<'bulkUpdateProduct'>(true);
	});

	router.patch('/bulk/product-test', scopeMiddleware('batchWrite'), async (req) => {
		await Promise.all(
			req.body.ids.map(async (id) => {
				const productTest = await new ProductTestController().findByProduct(id);
				if (!productTest) return;
				await new ProductTestController().update(productTest, req.body.data);
			}),
		);
		return respond<'bulkUpdateProductTests'>(true);
	});

	router.patch('/bulk/product-test/printInfo', scopeMiddleware('productTest'), async (req) => {
		await Promise.all(
			req.body.map(async ({ productId, productEnvelopeId }) => {
				const productTest = await new ProductTestController().findByProduct(productId);
				if (!productTest) return;
				await new ProductTestController().update(productTest, { printedWithEnvelopeId: productEnvelopeId });
			}),
		);
		return respond<'bulkUpdateProductTestPrintInfo'>(true);
	});

	router.patch('/product-test/:productId', scopeMiddleware('productTest'), async (req, res) => {
		const productTest = await new ProductTestController().findByProduct(req.params.productId);
		if (!productTest) return res.status(404).json();
		await new ProductTestController().update(productTest, req.body);
		return respond<'updateProductTest'>(true);
	});

	router.patch('/:productId', scopeMiddleware('productWrite', 'productTest', 'serviceWrite', 'warrantyClaimWrite'), async (req, res) => {
		const product = await new ProductController().update(req.params.productId, req.body);
		if (!product) return res.status(404).json();
		respond<'updateProduct'>(product);
	});

	router.patch('/:productId/files', scopeMiddleware('productTest', 'productWrite'), async (req) => {
		const fileIds = req.body;
		const productId = req.params.productId;
		await new ProductController().addFiles(productId, fileIds);
		respond<'addFilesToProduct'>(true);
	});

	router.get(
		'/:productId/test',
		scopeMiddleware('testRead', 'productTest', 'serviceRead', 'serviceWrite', 'warrantyClaimRead', 'warrantyClaimWrite'),
		async (req, res) => {
			const productTest = await new ProductTestController().findByProduct(req.params.productId);
			if (!productTest) return res.status(404).json();
			respond<'getProductTest'>(productTest);
		},
	);

	router.get(
		'/:productId/cosmetic-defects',
		scopeMiddleware('testRead', 'productTest', 'serviceRead', 'serviceWrite', 'warrantyClaimRead', 'warrantyClaimWrite'),
		async (req) => {
			const productCosmeticDefects = await new ProductCosmeticDefectController().findByProduct(req.params.productId, listAll());

			respond<'getProductCosmeticDefects'>(productCosmeticDefects);
		},
	);

	router.patch('/bulk/test', scopeMiddleware('envelopeWrite'), async (req) => {
		const productIds = req.body;
		const productEnvelopeMap: [Product['id'], ProductEnvelope['id'], Batch['id']][] = [];
		const errors: string[] = [];

		const [products] = await new ProductController().list(listAll({ filter: { id: { eq: productIds }, status: { eq: 'TESTED' } } }));

		for (const product of products) {
			if (!product?.code) {
				errors.push('Nelze dokončit testování, bez PCN produktu');
				continue;
			}
			const productCode = formatProductCode(product.code);

			const [attributeValues] = await new ProductController().getAttributeValues(product.id, listAll());
			const isAnyAttributeValueResolved = attributeValues.some((attributeValue) =>
				attributeValue.products.some((product) => product.type === 'resolved'),
			);

			if (!isAnyAttributeValueResolved) {
				errors.push(`${productCode}: Nelze dokončit testování, bez předešlého zpracování parametrů.`);
				continue;
			}

			if (!product.productCategoryId) throw new Error('Product is not in a category');
			const [attributes] = await new AttributeController().listByCategory(
				product.productCategoryId,
				listAll({ filter: { 'categoryAttributes.type': { eq: 'mandatory' } } }),
			);

			const allMandatoryAttributesAreResolved = attributes.every((attribute) =>
				attributeValues.some(
					(attributeValue) =>
						attributeValue.attribute.id === attribute.id &&
						attributeValue.products.some((product) => product.type === 'resolved') &&
						!attributeValue.temporary,
				),
			);

			if (!allMandatoryAttributesAreResolved) {
				errors.push(`${productCode}: Nelze dokončit testování, všechny povinné parametry (s hvězdičkou) je potřeba vyplnit.`);
				continue;
			}

			const batch = await new ProductController().getBatch(product);
			if (!batch) {
				errors.push(`${productCode}: Produkt není v žádné várce.`);
				continue;
			}

			const productEnvelope = await new ProductEnvelopeController().findOrCreate(product);

			const nextStatus = await new ProductController().handleClosingTest({
				product,
				attributeValues,
				batch,
				preventWarrantyClaim: false,
			});

			await new ProductController().update(product, {
				status: nextStatus,
				productEnvelope: { id: productEnvelope.id },
				productEnvelopeAssignedAt: new Date(),
				productEnvelopeAssignedBy: useAuthentication()?.user ?? null,
			});

			productEnvelopeMap.push([product.id, productEnvelope.id, batch.id]);
		}

		const batchIds = productEnvelopeMap.map(([, , batchId]) => batchId);
		await Promise.all(
			batchIds.map(async (batchId) => {
				// Set status of batch to 'CLOSED' if all products are tested
				const unTestedProductsCount = await new ProductController().count(
					listAll({ filter: { batchId: { eq: batchId }, status: { ne: getProductStatusesAbove('TESTED') } } }),
				);

				if (unTestedProductsCount === 0) {
					await new BatchController().update(batchId, {
						testedAt: new Date(),
						status: 'CLOSED',
					});
				}
			}),
		);

		respond<'bulkFinishProductTest'>({ data: productEnvelopeMap, errors });
	});

	router.patch('/:productId/test', scopeMiddleware('envelopeWrite'), async (req, res) => {
		const { productId } = req.params;
		const authentication = useAuthentication();
		if (!authentication) return res.status(401).json();

		const product = await new ProductController().findById(productId);

		if (!product?.code) {
			return res.status(400).json({ _error: { message: 'Nelze dokončit testování, bez PCN produktu' } });
		}

		if (!product.productCategoryId) throw new Error('Product is not in a category');
		const productCategory = await new ProductCategoryController().findById(product.productCategoryId);
		const categoryMinimumTestPhotos = productCategory?.minimumTestPhotos ?? 1;
		const [productImages] = await new FileController().listProductImages(productId, listAll());

		if (productImages.length < categoryMinimumTestPhotos) {
			return res
				.status(400)
				.json({ _error: { message: `Nahrajte alespoň ${categoryMinimumTestPhotos} obrázků dle pracovního postupu` } });
		}

		const productTasks = await new ProductTaskController().findByProduct(
			req.params.productId,
			listOne({ filter: { status: { eq: 'NEW' } } }),
		);

		if (productTasks.length > 0) {
			return res
				.status(400)
				.json({ _error: { message: 'Nelze dokončit testování, aniž by byly uzavřeny všechny úkoly na produktu' } });
		}

		const [attributeValues] = await new ProductController().getAttributeValues(productId, listAll());
		const isAnyAttributeValueResolved = attributeValues.some((attributeValue) =>
			attributeValue.products.some((product) => product.type === 'resolved'),
		);

		if (!isAnyAttributeValueResolved) {
			return res.status(400).json({ _error: { message: 'Nelze dokončit testování, bez předešlého zpracování parametrů.' } });
		}

		const categoryId = product.productCategoryId;
		const [attributes] = await new AttributeController().listByCategory(categoryId, listAll());

		const mandatoryAttributes = attributes.filter((attribute) =>
			attribute.categoryAttributes.some((categoryAttribute) => categoryAttribute.type === 'mandatory'),
		);

		const allMandatoryAttributesAreResolved = mandatoryAttributes.every((attribute) =>
			attributeValues.some(
				(attributeValue) =>
					attributeValue.attribute.id === attribute.id &&
					attributeValue.products.some((product) => product.type === 'resolved') &&
					!attributeValue.temporary,
			),
		);

		if (!allMandatoryAttributesAreResolved) {
			return res
				.status(400)
				.json({ _error: { message: 'Nelze dokončit testování, všechny povinné parametry (s hvězdičkou) je potřeba vyplnit.' } });
		}

		const batch = await new ProductController().getBatch(productId);
		if (!batch) {
			return res.status(404).json({ _error: { message: 'Produkt není v žádné várce.' } });
		}

		const productEnvelope = await new ProductEnvelopeController().findOrCreate(product, req.body.gradeId);
		const productTest = await new ProductTestController().findByProduct(productId);
		if (!productTest) return res.status(404).json({ _error: { message: 'Nenalezen záznam testování.' } });
		await new ProductTestController().update(productTest, {
			testedAt: new Date(),
			testedBy: productTest.testedById ? undefined : { id: authentication.user.id },
		});

		await new ProductController().validateProductCosmeticDefectsPictures(productId);

		const nextStatus = await new ProductController().handleClosingTest({
			product,
			attributeValues,
			batch,
			preventWarrantyClaim: req.body.preventWarrantyClaim,
		});

		const updatedProduct = await new ProductController().update(productId, {
			status: nextStatus,
			productEnvelope: { id: productEnvelope.id },
			productEnvelopeAssignedAt: new Date(),
			productEnvelopeAssignedBy: useAuthentication()?.user ?? null,
			...(req.body.gradeId ? { grade: { id: req.body.gradeId } } : {}),
		});

		// Set status of batch to 'CLOSED' if all products are tested
		const unTestedProductsCount = await new ProductController().count(
			listAll({ filter: { batchId: { eq: batch.id }, status: { ne: getProductStatusesAbove('TESTED') } } }),
		);

		if (unTestedProductsCount === 0) {
			await new BatchController().update(batch.id, {
				testedAt: new Date(),
				status: 'CLOSED',
			});
		}

		respond<'finishProductTest'>(updatedProduct);
	});

	router.put('/:productId/test', scopeMiddleware('productTest', 'productTestLead'), async (req, res) => {
		const authentication = useAuthentication();
		if (!authentication) return res.status(401).json();
		const { productId } = req.params;

		const productTest = await new ProductTestController().findByProduct(productId);
		if (!productTest) return res.status(404).json({ _error: { message: 'Nenalezen záznam testování.' } });

		const product = await new ProductController().findById(productId);

		if (!product?.code) {
			return res.status(400).json({ _error: { message: 'Nelze dokončit testování, bez PCN produktu' } });
		}

		if (!product.productCategoryId) throw new Error('Product is not in a category');
		const productCategory = await new ProductCategoryController().findById(product.productCategoryId);
		const categoryMinimumTestPhotos = productCategory?.minimumTestPhotos ?? 1;
		const [productImages] = await new FileController().listProductImages(productId, listAll());

		if (productImages.length < categoryMinimumTestPhotos) {
			return res
				.status(400)
				.json({ _error: { message: `Nahrajte alespoň ${categoryMinimumTestPhotos} obrázků dle pracovního postupu` } });
		}

		const [attributeValues] = await new ProductController().getAttributeValues(productId, listAll());
		const isAnyAttributeValueResolved = attributeValues.some((attributeValue) =>
			attributeValue.products.some((product) => product.type === 'resolved'),
		);

		if (!isAnyAttributeValueResolved) {
			return res.status(400).json({ _error: { message: 'Nelze dokončit testování, bez předešlého zpracování parametrů.' } });
		}

		const [attributes] = await new AttributeController().listByCategory(product.productCategoryId, listAll());

		const mandatoryAttributes = attributes.filter((attribute) =>
			attribute.categoryAttributes.some((categoryAttribute) => categoryAttribute.type === 'mandatory'),
		);

		const allMandatoryAttributesAreResolved = mandatoryAttributes.every((attribute) =>
			attributeValues.some(
				(attributeValue) =>
					attributeValue.attribute.id === attribute.id &&
					attributeValue.products.some((product) => product.type === 'resolved') &&
					!attributeValue.temporary,
			),
		);

		if (!allMandatoryAttributesAreResolved) {
			return res
				.status(400)
				.json({ _error: { message: 'Nelze dokončit testování, všechny povinné parametry (s hvězdičkou) je potřeba vyplnit.' } });
		}

		await new ProductController().validateProductCosmeticDefectsPictures(productId);

		await new ProductTestController().update(productTest, {
			testedAt: new Date(),
			testedBy: productTest.testedById ? undefined : { id: authentication.user.id },
		});

		const updatedProduct = await new ProductController().update(productId, {
			status: 'TESTED',
			...(req.body.gradeId ? { grade: { id: req.body.gradeId } } : {}),
		});

		respond<'saveProductTest'>(updatedProduct);
	});

	router.patch('/:productId/stock/attribute-values', scopeMiddleware('productWrite'), async (req, res) => {
		const { productId } = req.params;

		let product = await new ProductController().findById(productId);
		if (!product) return res.status(404).json();

		const oldStatus = product.status;
		const oldProductEnvelopeId = product.productEnvelopeId;

		if (oldProductEnvelopeId) {
			const newProductEnvelope = await new ProductEnvelopeController().findOrCreate(product);

			if (oldProductEnvelopeId !== newProductEnvelope.id) {
				product = await new ProductController().update(productId, {
					status: 'STOCK',
					productEnvelope: { id: newProductEnvelope.id },
					productEnvelopeAssignedAt: new Date(),
					productEnvelopeAssignedBy: useAuthentication()?.user ?? null,
				});

				await triggerShoptetExport(oldProductEnvelopeId);

				if (oldStatus === 'FOR_SALE') {
					const userId = useAuthentication()?.user.id;
					await new NotificationController().notify({
						data: {
							body: 'Karta produktu by se měla exportovat do Shoptetu.',
							href: `/product/envelope/${newProductEnvelope.id}`,
						},
						userId,
						notificationType: 'SHOPTET_EXPORT',
					});
				}
			}
		}

		respond<'editProductAttributeValues'>(product);
	});

	router.post('/:productId/template', scopeMiddleware('productTest', 'admin'), async (req, res) => {
		const { productId } = req.params;

		const template = await new ConditionalAttributeController().createProductTemplate(
			productId,
			req.body.attributeId,
			req.body.conditionalAttributeIds,
		);
		if (!template) return res.status(400).json({ _error: { message: 'Není možné vytvořit šablonu produktu.' } });

		respond<'createProductTemplate'>(true);
	});

	router.get(
		'/:productId/attribute-value',
		scopeMiddleware(
			'testRead',
			'productTest',
			'productRead',
			'productWrite',
			'batchRead',
			'batchWrite',
			'productTestLead',
			'batchCheck',
			'batchDelivery',
			'stock',
		),
		async (req) => {
			const [attributeValues] = await new ProductController().getAttributeValues(req.params.productId, getListProps());

			// FIXME - If there is one attribute value for more than one type, by allocating the attributeValueType, we lost that information.
			//         This reduce temporarily fixes it until I can do better solution, which doesn't depend on this utility value...
			const transformedAttributeValues = attributeValues.reduce<
				(AttributeValue & { attributeValueType: ProductAttributeValue['type']; autofill: boolean; lock: boolean })[]
			>((acc, attributeValue) => {
				attributeValue.products.forEach((productAttributeValue) => {
					acc.push({
						...attributeValue,
						attributeValueType: productAttributeValue.type,
						autofill: productAttributeValue.autofill,
						lock: productAttributeValue.lock,
						products: [productAttributeValue],
					});
				});
				return acc;
			}, []);

			respond<'getProductAttributeValues'>(transformedAttributeValues);
		},
	);

	router.patch('/bulk/attribute-values', scopeMiddleware('admin'), async (req) => {
		const { productIds, attributeValues } = req.body;

		await Promise.all(
			productIds.map(async (productId) => {
				await new ProductController().handleImportAttributeValuesUpdate(productId, attributeValues);
			}),
		);

		respond<'bulkAssignAttributeValues'>(true);
	});

	router.put('/:productId/attribute-value', scopeMiddleware('productTest'), async (req) => {
		const { attributeValues } = req.body;

		const textValues = attributeValues.filter((item) => !!item.value && !item.attributeValueId);
		await Promise.all(
			textValues.map(async (item) => {
				if (!item.value) return;
				const attributeValue = await new AttributeController().createTextValue(item.attributeId, String(item.value));
				item.attributeValueId = attributeValue.id; // Intentional side effect
			}),
		);

		await new ProductController().resolveAttributeValues(
			req.params.productId,
			attributeValues.filter(
				(item): item is { attributeValueId: string; attributeId: string; isFix: boolean } => !!item.attributeValueId,
			),
		);

		for (const { value, attributeId, attributeValueId } of attributeValues) {
			if (!attributeValueId || !value) continue;
			await new AttributeValueController(attributeId).createAlternative(attributeValueId, { value });
		}

		await new ProductController().handleAttributeValuesMismatchFlags(req.params.productId);

		respond<'updateProductAttributeValues'>(true);
	});

	router.post('/:productId/attribute-value', scopeMiddleware('productTest'), async (req) => {
		const attributeValues = Array.isArray(req.body) ? req.body : [req.body];

		for (const attributeValue of attributeValues) {
			if (attributeValue.value !== undefined && !attributeValue.attributeValueId) {
				const newAttributeValue = await new AttributeController().createTextValue(
					attributeValue.attributeId,
					String(attributeValue.value),
				);
				attributeValue.attributeValueId = newAttributeValue.id; // Intentional side effect
			}
		}

		const assignableAttributeValues = attributeValues.filter(
			(
				attributeValue,
			): attributeValue is { attributeId: string; attributeValueId: string; autofill: boolean; lock: boolean; isFix: boolean } =>
				!!attributeValue.attributeValueId,
		);

		if (assignableAttributeValues.length > 0) {
			await new ProductController().setAttributeValue(
				req.params.productId,
				assignableAttributeValues,
				'resolved',
				assignableAttributeValues[0].autofill,
				assignableAttributeValues[0].lock,
				undefined,
				assignableAttributeValues[0].isFix,
			);
		}

		await new ProductController().handleAttributeValuesMismatchFlags(req.params.productId);

		respond<'setProductAttributeValue'>(true);
	});

	router.post('/bulk/envelope', scopeMiddleware('productWrite', 'batchWrite'), async (req) => {
		await Promise.all(
			req.body.ids.map(async (productId) => {
				await new ProductController().assignProductEnvelope(productId, req.body.data.productEnvelopeId);
			}),
		);

		respond<'bulkAssignProductEnvelope'>(true);
	});

	router.post('/:productId/envelope', scopeMiddleware('productWrite', 'batchWrite'), async (req) => {
		await new ProductController().assignProductEnvelope(req.params.productId, req.body.productEnvelopeId);

		respond<'assignProductEnvelope'>(true);
	});

	router.patch('/:productId/attribute-value-autofill', scopeMiddleware('productTest'), async (req) => {
		const product = await new ProductController().findById(req.params.productId);
		if (!product) return respond<'removeProductAttributeValueAutofill'>(false);

		const [autofilledProductAttributeValues] = await new ProductAttributeValueController().list(
			listAll({
				filter: {
					type: { eq: 'resolved' },
					productId: { eq: product.id },
					autofill: { eq: true },
				},
			}),
		);

		if (autofilledProductAttributeValues.length > 0) {
			await new ProductController().setAttributeValue(
				product.id,
				autofilledProductAttributeValues.map((productAttributeValue) => ({
					attributeValueId: productAttributeValue.attributeValue.id,
					attributeId: productAttributeValue.attributeValue.attribute.id,
				})),
				'resolved',
				false,
			);
		}

		respond<'removeProductAttributeValueAutofill'>(true);
	});

	router.delete('/:productId/attribute-value/:attributeValueId', scopeMiddleware('productTest'), async (req) => {
		const { productId, attributeValueId } = req.params;
		await new ProductController().removeAttributeValue(productId, attributeValueId, 'resolved');

		respond<'removeProductAttributeValue'>(true);
	});

	router.patch('/:productId/bulk/attribute-values', scopeMiddleware('admin', 'batchWrite', 'productWrite'), async (req) => {
		const noMatchAttribute = await new AttributeController().findOrCreateNoMatchAttribute();
		const updatedProduct = await new ProductController().update(req.params.productId, {
			productCategory: { id: req.body.productCategoryId },
		});
		const { attributeValues, unmatchedAttributeValues } = req.body;
		const changedUnmatchedAttributeValues = unmatchedAttributeValues.filter(({ attributeId }) => attributeId !== noMatchAttribute.id);
		const allAttributeValues = uniquesBy([...changedUnmatchedAttributeValues, ...attributeValues], 'attributeId');
		const attributeIds = allAttributeValues.map(({ attributeId }) => attributeId);
		const textValues = allAttributeValues.filter((item) => !!item.value && !item.attributeValueId);

		await Promise.all(
			textValues.map(async (item) => {
				if (!item.value) return;
				const attributeValue = await new AttributeController().createTextValue(item.attributeId, String(item.value));
				item.attributeValueId = attributeValue.id; // Intentional side effect
			}),
		);

		const [existingProductAttributeValues] = await new ProductAttributeValueController().list(
			listAll({
				filter: {
					type: { eq: 'import' },
					productId: { eq: updatedProduct.id },
					'attribute.id': { eq: [...attributeIds, noMatchAttribute.id] },
				},
			}),
		);

		const changedAttributeValues = allAttributeValues.filter(({ attributeValueId, attributeId }) => {
			const existingProductAttributeValue = existingProductAttributeValues.find(
				({ attributeValue }) => attributeValue.attribute.id === attributeId,
			);
			if (!existingProductAttributeValue) return true;

			return existingProductAttributeValue.attributeValue.id !== attributeValueId;
		});
		const changedAttributeIds = changedAttributeValues.map(({ attributeId }) => attributeId);

		await new ProductAttributeValueController().delete(
			existingProductAttributeValues
				.filter(({ attributeValue }) => {
					if (changedAttributeIds.includes(attributeValue.attribute.id)) return true;
					// If the attribute is not changed, it means, it is the _NO_MATCH_ one, so we have to check specific values
					return !!changedUnmatchedAttributeValues.find(
						({ unmatchedAttributeValueId }) => unmatchedAttributeValueId === attributeValue.id,
					);
				})
				.map(({ id }) => id),
		);

		await new ProductController().handleImportAttributeValuesUpdate(updatedProduct.id, allAttributeValues);

		respond<'assignAttributeValuesToProduct'>(true);
	});

	router.post('/:productId/attribute-value-loader', scopeMiddleware('productTest'), async (req, res) => {
		const { productId } = req.params;
		const { fileIds, forceAppendSn, forceModelOverride } = req.body;
		const data = (await Promise.all(fileIds.map((fileId) => reader(fileId)))).filter((data) => data !== false);
		if (data.length === 0) return respond<'loadAttributeValuesFromReader'>(false);

		const product = await new ProductController().findWithAttributeValues(productId);

		if (!product || !product.productCategoryId) return respond<'loadAttributeValuesFromReader'>(false);

		const [categoryAttributes] = await new AttributeController().listByCategory(product.productCategoryId, listAll());

		const attributeValues = uniquesBy(
			data.flatMap((data) => data?.attributeValues),
			'attributeId',
		).filter(({ attributeId }) => categoryAttributes.some((attribute) => attribute.id === attributeId));

		const props = data
			.flatMap((data) => data?.props)
			.reduce<Partial<Product>>((acc, props) => {
				(Object.keys(props) as unknown as (keyof Product)[]).forEach((key) => {
					if (props[key]) {
						// @ts-expect-error - 🤷‍♂️
						acc[key] = props[key];
					}
				});
				return acc;
			}, {});

		if (props.sn) {
			const sn = props.sn.toUpperCase();

			const batchProductWithSameSN = await new ProductController().findBySnInBatch(product.batchId as string, sn);

			if (batchProductWithSameSN && batchProductWithSameSN.id !== product.id) {
				return res.status(409).json({ sn, conflictingProductId: batchProductWithSameSN.id });
			}

			if (sn !== product.sn && !sn.includes(product.sn) && !product.sn.includes(sn)) {
				if (forceAppendSn && product.sn.length > 0) {
					props.sn = product.sn + '|' + props.sn;
				} else {
					return res.status(500).json({ sn: { current: product.sn, new: sn } });
				}
			}
		}

		const modelAttribute = categoryAttributes.find((item) => item.displayName === 'Model');
		const modelAttributeValueFromReader = attributeValues.find((attributeValue) => attributeValue.attributeId === modelAttribute?.id);
		const modelAttributeValueFromProduct = product.attributeValues.find(
			(item) => item.type === 'resolved' && item.attributeValue.attribute.id === modelAttribute?.id,
		);

		if (!forceModelOverride && modelAttributeValueFromReader && modelAttributeValueFromProduct) {
			const modelsMatch = modelAttributeValueFromReader.attributeValueId === modelAttributeValueFromProduct?.attributeValueId;

			if (!modelsMatch) {
				const [batchProductsWithReaderModel] = await new ProductController().findByAttributeValues(
					[modelAttributeValueFromReader.attributeValueId],
					listAll({ filter: { id: { ne: product.id }, batchId: { eq: product.batchId }, status: { eq: 'TO_TEST' } } }),
				);

				const readerModelValue = await new AttributeValueController().findById(modelAttributeValueFromReader.attributeValueId);
				const productModelValue = await new AttributeValueController().findById(modelAttributeValueFromProduct.attributeValueId);

				return res.status(300).json({
					readerModel: readerModelValue?.value.toString() ?? '',
					productModel: productModelValue?.value.toString() ?? '',
					matchingProducts: batchProductsWithReaderModel.map((product) => ({
						id: product.id,
						code: product.code?.code ?? null,
					})),
				});
			}
		}

		const newProduct = await new ProductController().update(productId, props);
		await new ProductController().addFiles(productId, fileIds, 'reader');
		await new ProductController().setAttributeValue(newProduct.id, attributeValues, ['reader', 'resolved']);

		respond<'loadAttributeValuesFromReader'>(true);
	});

	router.post('/attribute-value-loader', scopeMiddleware('productTest'), async (req, res) => {
		const { fileIds } = req.body;
		const data = (await Promise.all(fileIds.map((fileId) => reader(fileId)))).filter((data) => data !== false);

		if (data.length === 0) return res.status(500).json();

		const props = data
			.flatMap((data) => data?.props)
			.reduce<Partial<Product>>((acc, props) => {
				(Object.keys(props) as unknown as (keyof Product)[]).forEach((key) => {
					if (props[key]) {
						// @ts-expect-error - 🤷‍♂️
						acc[key] = props[key];
					}
				});
				return acc;
			}, {});

		if (!props.sn) return res.status(404).json();

		const sn = props.sn.toUpperCase();
		const product = await new ProductController().findBySn(sn, { filter: { status: { eq: 'TO_TEST' } } });

		if (!product || !product.productCategoryId) return res.status(404).json();

		const [categoryAttributes] = await new AttributeController().listByCategory(product.productCategoryId, listAll());

		const attributeValues = uniquesBy(
			data.flatMap((data) => data?.attributeValues),
			'attributeId',
		).filter(({ attributeId }) => categoryAttributes.some((attribute) => attribute.id === attributeId));

		const newProduct = await new ProductController().update(product, props);
		await new ProductController().addFiles(newProduct.id, fileIds, 'reader');
		await new ProductController().setAttributeValue(newProduct.id, attributeValues, ['reader', 'resolved']);

		respond<'loadAttributeValuesFromCLI'>(sn);
	});

	router.delete('/bulk', scopeMiddleware('batchWrite'), async (req) => {
		await new ProductController().delete(req.body.ids);
		respond<'bulkDeleteProducts'>(true);
	});

	router.delete('/:productId', scopeMiddleware('admin', 'batchWrite'), async (req) => {
		const result = await new ProductController().delete(req.params.productId);

		respond<'deleteProduct'>(result);
	});

	router.delete('/:productId/files/:fileId', scopeMiddleware('productWrite', 'productTest'), async (req, res) => {
		const entity = await new ProductController().deleteFile(req.params.productId, req.params.fileId);
		if (!entity) return res.status(500).send();

		return respond<'deleteProductFile'>(true);
	});

	router.get('/:productId/service-case', scopeMiddleware('serviceRead', 'serviceWrite'), async (req, res) => {
		const serviceCase = await new ServiceCaseController().getServiceCaseByProduct(req.params.productId);
		if (!serviceCase) return res.status(404).json();

		respond<'getProductServiceCase'>(serviceCase);
	});

	router.get('/:productId/warranty-claim', scopeMiddleware('warrantyClaimRead', 'warrantyClaimWrite'), async (req, res) => {
		const warrantyClaim = await new WarrantyClaimController().getWarrantyClaimByProduct(req.params.productId);
		if (!warrantyClaim) return res.status(404).json();

		respond<'getProductWarrantyClaim'>(warrantyClaim);
	});

	router.patch('/bulk/warranty', scopeMiddleware('admin'), async (req) => {
		const { productIds, data } = req.body;
		const { expiredAt, type } = data;

		await Promise.all(
			productIds.map(async (productId) => {
				await new WarrantyController().findOrCreate(productId, { type, expiredAt });
			}),
		);

		respond<'bulkUpdateProductWarranty'>(true);
	});

	return router;
};
