import { AttributeController, ProductCategoryAttributeController, ProductCategoryController } from '@pocitarna-nx-2023/database';
import { productCategoryApi } from '@pocitarna-nx-2023/zodios';
import type { zodiosContext } from '@zodios/express';
import { scopeMiddleware } from '../middlewares/scope';
import { getListProps } from '../utils/getListProps';
import { respond, respondWithPaging } from '../utils/respond';

export const productCategoryRouter = (ctx: ReturnType<typeof zodiosContext>) => {
	const router = ctx.router(productCategoryApi);

	router.get('/', scopeMiddleware('admin', 'productRead', 'productWrite', 'testRead', 'productTest', 'productAdmin'), async () => {
		const productCategories = await new ProductCategoryController().listAll();
		respond<'getProductCategories'>(productCategories);
	});

	router.get(
		'/list',
		scopeMiddleware('admin', 'batchRead', 'productRead', 'productWrite', 'testRead', 'productTest', 'productAdmin'),
		async () => {
			const [productCategories, getCount] = await new ProductCategoryController().listFlat(getListProps());
			respondWithPaging<'getProductCategoriesList'>(productCategories, await getCount());
		},
	);

	router.post('/', scopeMiddleware('admin', 'productAdmin'), async (req) => {
		await new ProductCategoryController().create(req.body);

		respond<'createProductCategory'>(true);
	});

	router.put('/:productCategoryId', scopeMiddleware('admin', 'productAdmin'), async (req, res) => {
		const productCategory = await new ProductCategoryController().update(req.params.productCategoryId, req.body);
		if (!productCategory) return res.status(404).json();

		respond<'updateProductCategory'>(productCategory);
	});

	router.get(
		'/:productCategoryId',
		scopeMiddleware(
			'admin',
			'stock',
			'productRead',
			'productWrite',
			'batchRead',
			'batchWrite',
			'testRead',
			'productTest',
			'serviceRead',
			'serviceWrite',
			'warrantyClaimRead',
			'warrantyClaimWrite',
			'productAdmin',
		),
		async (req, res) => {
			const productCategory = await new ProductCategoryController().findById(req.params.productCategoryId);
			if (!productCategory) return res.status(404).json();

			respond<'getProductCategory'>(productCategory);
		},
	);

	router.delete('/:productCategoryId', scopeMiddleware('admin', 'productAdmin'), async (req, res) => {
		const category = await new ProductCategoryController().listDescendantsTree(req.params.productCategoryId);
		if (!category) return res.status(404).json();
		if (category.children.length > 0) return res.status(400).json({ _error: { message: 'Category has children.' } });

		const result = await new ProductCategoryController().delete(req.params.productCategoryId);
		respond<'deleteProductCategory'>(result);
	});

	router.get(
		'/:productCategoryId/ancestors',
		scopeMiddleware('admin', 'stock', 'productRead', 'productWrite', 'batchRead', 'batchWrite', 'productAdmin'),
		async (req, res) => {
			const productCategory = await new ProductCategoryController().listAncestorsTree(req.params.productCategoryId);
			if (!productCategory) return res.status(404).json();

			respond<'getProductCategoryAncestors'>(productCategory);
		},
	);

	router.get(
		'/:productCategoryId/descendants',
		scopeMiddleware('admin', 'stock', 'productRead', 'productWrite', 'batchRead', 'batchWrite', 'productAdmin'),
		async (req, res) => {
			const productCategory = await new ProductCategoryController().listDescendantsTree(req.params.productCategoryId);
			if (!productCategory) return res.status(404).json();

			respond<'getProductCategoryDescendants'>(productCategory);
		},
	);

	router.get(
		'/:productCategoryId/descendants-array',
		scopeMiddleware('admin', 'stock', 'productRead', 'productWrite', 'batchRead', 'batchWrite', 'productAdmin'),
		async (req, res) => {
			const productCategories = await new ProductCategoryController().listDescendantsArray(req.params.productCategoryId);
			if (!productCategories) return res.status(404).json();

			respond<'getProductCategoryDescendantsArray'>(productCategories.map((category) => category.id));
		},
	);

	router.get(
		'/:productCategoryId/siblings',
		scopeMiddleware('admin', 'stock', 'productRead', 'productWrite', 'batchRead', 'batchWrite', 'productAdmin'),
		async (req, res) => {
			const productCategory = await new ProductCategoryController().listAncestorsTree(req.params.productCategoryId);
			if (!productCategory) return res.status(404).json();

			if (!productCategory.parent?.id) return null;
			const siblingProductCategory = await new ProductCategoryController().listDescendantsTree(productCategory.parent.id);
			if (!siblingProductCategory) return res.status(404).json();

			respond<'getProductCategorySiblings'>(siblingProductCategory);
		},
	);

	router.patch('/:productCategoryId', scopeMiddleware('admin', 'productAdmin'), async (req, res) => {
		const productCategory = await new ProductCategoryController().update(req.params.productCategoryId, {
			parent: { id: req.body.parentId },
		});
		if (!productCategory) return res.status(404).json();

		respond<'moveProductCategory'>(productCategory);
	});

	router.get(
		'/:productCategoryId/attribute',
		scopeMiddleware(
			'admin',
			'stock',
			'productRead',
			'productWrite',
			'testRead',
			'productTest',
			'batchRead',
			'batchWrite',
			'productAdmin',
		),
		async (req) => {
			const [attributes, getCount] = await new AttributeController().listByCategory(req.params.productCategoryId, getListProps());

			respondWithPaging<'getProductCategoryAttributes'>(attributes, await getCount());
		},
	);

	router.post('/:productCategoryId/attribute', scopeMiddleware('admin', 'productAdmin'), async (req, res) => {
		const { attributeId, ...data } = req.body;
		const result = await new ProductCategoryController().addAttribute(req.params.productCategoryId, attributeId, data);
		if (!result) return res.status(500).json();

		respond<'addProductCategoryAttribute'>(result);
	});

	router.get('/:productCategoryId/import-file', scopeMiddleware('admin', 'productAdmin'), async (req, res) => {
		const file = await new ProductCategoryController().createImportFile(req.params.productCategoryId);
		const category = await new ProductCategoryController().findById(req.params.productCategoryId);

		if (!category) return res.status(404).json();

		res.setHeader('Content-Type', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
		res.setHeader('Content-Disposition', `attachment; filename=${category.name}-template.xlsx`);
		res.send(file);
	});

	router.delete('/:productCategoryId/attribute/:attributeId', scopeMiddleware('admin', 'productAdmin'), async (req) => {
		if (req.body.type) {
			const result = await new ProductCategoryController().removeAttribute(
				req.params.productCategoryId,
				req.params.attributeId,
				req.body.type,
			);

			return respond<'deleteProductCategoryAttribute'>(result);
		}

		const result = await new ProductCategoryController().removeAttributes(req.params.productCategoryId, req.params.attributeId);
		respond<'deleteProductCategoryAttribute'>(result);
	});

	router.patch('/:productCategoryId/attribute-sequence', scopeMiddleware('admin', 'productAdmin'), async (req, res) => {
		const result = await new ProductCategoryAttributeController().updateSequence(
			req.params.productCategoryId,
			req.body.attributeId,
			req.body.sequence,
		);
		if (!result) return res.status(500).json();

		respond<'updateProductCategoryAttributeSequence'>(result);
	});

	return router;
};
