import { ACTION_LABEL, HTTP_METHODS_NAMES, type HttpMethod } from '@pocitarna-nx-2023/config';
import { ActionController, useAuthentication } from '@pocitarna-nx-2023/database';
import { type RequestHandler } from 'express';
import { set as httpContextSet } from 'express-http-context';
import { asyncHandler } from '../utils/asyncHandler';

export const createActionMiddleware: RequestHandler = asyncHandler(async (req, _res, next) => {
	const authentication = useAuthentication();

	if (authentication && isValidHttpMethod(req.method)) {
		const action = await new ActionController().create({
			method: req.method,
			endpoint: req.path,
			authentication,
			description: req.body?.actionDescription ?? '',
		});
		httpContextSet(ACTION_LABEL, action);
	}

	next();
});

const isValidHttpMethod = (method: unknown): method is HttpMethod => {
	return method !== 'GET' && HTTP_METHODS_NAMES.includes(method as HttpMethod);
};
