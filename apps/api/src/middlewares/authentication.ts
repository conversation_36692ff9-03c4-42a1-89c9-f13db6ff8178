import { getSession } from '@pocitarna-nx-2023/aws';
import { type Authentication, AuthenticationController } from '@pocitarna-nx-2023/database';
import * as Sentry from '@sentry/node';
import { isPast } from 'date-fns';
import type { RequestHand<PERSON> } from 'express';
import * as httpContext from 'express-http-context';
import { asyncHandler } from '../utils/asyncHandler';

// Simple memory only cache for keeping sessions, can always fall back to database, don't care for consistency or freshness
const cache: Record<string, { session: NonNullable<Awaited<ReturnType<typeof getSession>>>; authentication: Authentication | null }> = {};

export const createAuthenticationMiddleware: RequestHandler = asyncHandler(async (req, _res, next) => {
	const sessionToken = req.cookies['next-auth.session-token'];
	if (!sessionToken) return next();

	if (cache[sessionToken] && !isPast(cache[sessionToken].session.expires)) {
		const authentication = cache[sessionToken].authentication;
		httpContext.set('authentication', authentication);
		if (authentication?.user) {
			Sentry.setUser({
				id: authentication.user.id,
				email: authentication.user.email ?? undefined,
				username: authentication.user.name,
			});
		}
		return next();
	}

	const session = await getSession(sessionToken);
	if (!session) return next();

	const authentication = await new AuthenticationController().findByIdWithRelations(session.authenticationId);
	httpContext.set('authentication', authentication);
	cache[sessionToken] = { session, authentication };
	if (authentication?.user) {
		Sentry.setUser({ id: authentication.user.id, email: authentication.user.email ?? undefined, username: authentication.user.name });
	}

	next();
});
