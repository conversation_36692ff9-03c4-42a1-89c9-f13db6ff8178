import './sentry';
import { ONE_MINUTE, ONE_SECOND, SSE_ENDPOINT, TWO_SECONDS } from '@pocitarna-nx-2023/config';
import { Database } from '@pocitarna-nx-2023/database';
import { sseMiddleware, sseRouter } from '@pocitarna-nx-2023/sse-server';
import { api } from '@pocitarna-nx-2023/zodios';
import * as Sentry from '@sentry/node';
import { zodiosContext } from '@zodios/express';
import bodyParser from 'body-parser';
import compression from 'compression';
import cookieParser from 'cookie-parser';
import cors from 'cors';
import type { NextFunction, Request, Response } from 'express';
import * as httpContext from 'express-http-context';
import helmet from 'helmet';
import morgan from 'morgan';
import { createActionMiddleware } from './middlewares/action';
import { createAuthenticationMiddleware } from './middlewares/authentication';
import { contextMiddleware } from './middlewares/contextMiddleware';
import { createTransactionMiddleware, rollbackTransaction } from './middlewares/transaction';
import { rootRouter } from './routes';

const ctx = zodiosContext();
const app = ctx.app(api, { enableJsonBodyParser: false });

// app.set('query parser', (str: string) => qs.parse(str, { arrayLimit: Infinity, strictNullHandling: true }));
app.set('query parser', (str: string) =>
	str && str.length > 0 && str.startsWith('q=') ? JSON.parse(decodeURIComponent(str.slice(2))) : {},
);

app.use(bodyParser.json({ limit: '100mb' }));
app.use(cors({ origin: true, credentials: true }));
app.use(cookieParser());
app.use(helmet({ crossOriginResourcePolicy: { policy: 'cross-origin' } }));
app.use(compression());
app.use(
	morgan(`:date[iso] - :remote-addr :method :url HTTP/:http-version :status :res[content-length]B - :response-time ms - :total-time ms`, {
		skip: (req) =>
			req.method === 'OPTIONS' ||
			req.originalUrl === '/' ||
			req.originalUrl === '/error' ||
			req.originalUrl === '/healthcheck' ||
			req.originalUrl.startsWith('/sse') ||
			(req.method === 'GET' && req.originalUrl.startsWith('/auth/') && !req.originalUrl.startsWith('/auth/admin')),
	}),
);
app.use(httpContext.middleware);
app.use(contextMiddleware);
app.use(createTransactionMiddleware);
app.use(createAuthenticationMiddleware);
app.use(createActionMiddleware);
app.use(sseMiddleware);
app.use('/', rootRouter(ctx));
app.use(SSE_ENDPOINT, sseRouter());

// @ts-expect-error - Sentry doesn't like augmented express
Sentry.setupExpressErrorHandler(app);

app.use(async (error: Error, req: Request, res: Response, _next: NextFunction) => {
	console.error(req.method, req.path, req.originalUrl, req.query, req.body, error);
	await rollbackTransaction();
	res.status(500).json({
		_error: {
			message: error.message,
			stack: error.stack,
		},
	});
});

Database.initialize().then(() => {
	const port = process.env['PORT'] || 3333;
	const server = app.listen(port);
	server.keepAliveTimeout = ONE_MINUTE + ONE_SECOND;
	server.headersTimeout = ONE_MINUTE + TWO_SECONDS;
	server.on('error', console.error);
});
