import { withSentry } from '@pocitarna-nx-2023/aws';
import { ONE_HOUR, TEN_MINUTES } from '@pocitarna-nx-2023/config';
import { listAll, NotificationController, ProductController, withDatabase } from '@pocitarna-nx-2023/database';
import { formatProductCode } from '@pocitarna-nx-2023/utils';
import { groupBy } from 'rambdax';

const getNotificationCode = (product: { id: string; pickedAt?: Date | null }) => `${product.id}-${product.pickedAt?.toISOString()}`;

export const handler = withSentry(
	'https://<EMAIL>/4509666423930960',
	withDatabase(async () => {
		const hourAgo = new Date(Date.now() - ONE_HOUR);
		const hourAndTenMinutesAgo = new Date(hourAgo.getTime() - TEN_MINUTES);
		const [products] = await new ProductController().list(
			listAll({ filter: { pickedAt: { lte: hourAgo, gt: hourAndTenMinutesAgo } } }),
		);

		const notificationKeys = products.map((product) => getNotificationCode(product));
		const [sentNotifications] = await new NotificationController().list(
			listAll({ filter: { notificationCode: { eq: notificationKeys } } }),
		);

		const unsentProducts = products.filter((product) => {
			if (!product.pickedAt || !product.pickedById) return false;
			const code = getNotificationCode(product);
			return !sentNotifications.some((n) => n.notificationCode === code);
		});

		if (unsentProducts.length === 0) return;

		const groupedProducts = groupBy((p) => p.pickedById as string, unsentProducts);

		await Promise.all(
			Object.entries(groupedProducts).map(([userId, userProducts]) =>
				new NotificationController().notify({
					notificationType: 'PRODUCT_PICKUP',
					userId,
					data: userProducts.map((product) => ({
						notificationCode: getNotificationCode(product),
						body: `Produkt ${formatProductCode(product.code)} nebyl zaskladněn na žádnou pozici ve skladu, najdi a zaskladni ho!`,
						href: `/product/${product.id}`,
					})),
				}),
			),
		);
	}),
);
